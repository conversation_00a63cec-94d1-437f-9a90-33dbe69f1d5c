{"/domain-pools/dynamic": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:create"]}], "tags": ["Domain Pool"], "summary": "Creates dynamic domain pools (game client domain pols)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainPoolData"}}], "responses": {"201": {"description": "Created dynamic domain pool info", "schema": {"$ref": "#/definitions/DynamicDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:view"]}], "tags": ["Domain Pool"], "summary": "Gets list of dynamic domain pools", "responses": {"200": {"description": "List of dynamic domain pools", "schema": {"type": "array", "items": {"$ref": "#/definitions/DynamicDomainPool"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/dynamic": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Updates dynamic domain pools (game server domain pools) - WARNING: this operation can trigger game server migration", "parameters": [{"$ref": "#/parameters/poolId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainPoolData"}}], "responses": {"201": {"description": "Updated dynamic domain pool info", "schema": {"$ref": "#/definitions/DynamicDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:view"]}], "tags": ["Domain Pool"], "summary": "Gets dynamic domain pool information", "parameters": [{"$ref": "#/parameters/poolId"}], "responses": {"200": {"description": "Dynamic domain pool info", "schema": {"$ref": "#/definitions/DynamicDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:remove"]}], "tags": ["Domain Pool"], "summary": "Deletes dynamic domain pool", "parameters": [{"$ref": "#/parameters/poolId"}], "responses": {"204": {"description": "Dynamic domain pool deleted successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/dynamic/{domainId}": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Adds dynamic domain pool item - WARNING: this operation can trigger game server migration", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Added dynamic domain pool item info", "schema": {"$ref": "#/definitions/DynamicDomainPoolItem"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Removes dynamic domain pool item - WARNING: this operation can trigger game server migration", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"204": {"description": "Dynamic domain pool item removed successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/dynamic/{domainId}/disable": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Disables dynamic domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Dynamic domain pool item disabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/dynamic/{domainId}/enable": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Enables dynamic domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Dynamic domain pool item enabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/domain-pools/dynamic": {"get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:view"]}], "tags": ["Domain Pool"], "summary": "Gets entity dynamic domain pool information", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/inherited"}], "responses": {"200": {"description": "Entity dynamic domain pool info", "schema": {"$ref": "#/definitions/DynamicDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:remove"]}], "tags": ["Domain Pool"], "summary": "Removes dynamic domain pool from an entity - WARNING: this operation can trigger game server migration", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"204": {"description": "Dynamic domain pool successfully removed from entity"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/domain-pools/{poolId}/dynamic": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:edit"]}], "tags": ["Domain Pool"], "summary": "Adds dynamic domain pool to an entity - WARNING: this operation can trigger game server migration", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/poolId"}], "responses": {"200": {"description": "Entity dynamic domain pool info", "schema": {"$ref": "#/definitions/DynamicDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}}
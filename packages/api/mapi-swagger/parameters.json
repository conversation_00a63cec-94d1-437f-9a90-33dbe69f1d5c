{"entityId": {"name": "entityId", "in": "path", "type": "string", "description": "Entity Id", "required": true}, "includeProxy": {"name": "includeProxy", "description": "Add to response info about Proxy", "type": "boolean", "in": "query"}, "includeJurisdiction": {"name": "includeJurisdiction", "description": "Add to operator response info about jurisdiction", "type": "boolean", "in": "query"}, "addAggregatedFinalLimits": {"name": "addAggregatedFinalLimits", "description": "If true then in response we return aggregated 'real' operator limits for this game, calculated on the base of configured game limits, operator limits, game limits filters, etc. If currency present then we will return limits only for that currency. (It works only for brands)", "type": "boolean", "in": "query"}, "skipJurisdictionFiltering": {"name": "skipJurisdictionFiltering", "description": "If true, the jurisdiction filtering based on the maxTotalStake defined in jurisdiction settings will be skipped", "type": "boolean", "in": "query"}, "additionalFields": {"name": "additionalFields", "description": "Add to response additional fields. Available fields: dynamicDomainId, staticDomainId, environment, defaultCountry, defaultCurrency, defaultLanguage", "type": "string", "in": "query"}, "includeDecryptedBrand": {"name": "includeDecryptedBrand", "description": "Add to response decrypted brand id if has user SuperAdmin role", "type": "boolean", "in": "query"}, "ip_header": {"name": "X-Forwarded-For", "description": "Client IP address", "type": "string", "in": "header"}, "encodedId": {"name": "encodedId", "in": "path", "type": "string", "description": "Encoded Id", "required": true}, "decodedId": {"name": "decodedId", "in": "path", "type": "number", "description": "Decoded Id", "required": true}, "path": {"name": "path", "in": "path", "description": "Business entity path", "required": true, "type": "string"}, "pathInQuery": {"name": "path", "in": "query", "description": "Business entity path", "required": false, "type": "string"}, "reportId": {"name": "reportId", "in": "path", "description": "Report's publicId", "required": true, "type": "string"}, "reportDomainsId": {"name": "reportDomainsId", "in": "path", "description": "Report domains publicId", "required": true, "type": "string"}, "reportType": {"name": "reportType", "in": "path", "description": "Report's type", "required": true, "type": "string"}, "AcceptLanguage": {"name": "Accept-Language", "in": "header", "description": "Language of Report", "required": false, "type": "string"}, "agentId": {"name": "agentId", "in": "path", "description": "Agent's public id", "required": true, "type": "string"}, "siteId": {"name": "siteId", "in": "path", "description": "Available Site public id", "required": true, "type": "string"}, "tokenId": {"name": "tokenId", "in": "path", "description": "Site token's public id", "required": true, "type": "string"}, "roleId": {"name": "roleId", "in": "path", "description": "Role's id", "required": true, "type": "string"}, "playerCode": {"name": "playerCode", "in": "path", "description": "Player code", "required": true, "type": "string"}, "brandIdPath": {"name": "brandId", "in": "path", "description": "brand id", "required": true, "type": "number"}, "brandPIDPath": {"name": "brandPID", "in": "path", "description": "Encoded brand id", "required": true, "type": "string"}, "gameCode": {"name": "gameCode", "in": "path", "description": "Game code", "required": true, "type": "string"}, "providerId": {"name": "providerId", "in": "path", "description": "Game provider id", "required": true, "type": "string"}, "paymentMethodCode": {"name": "code", "in": "path", "description": "Payment method code", "required": true, "type": "string"}, "gameGroup": {"name": "gameGroup", "in": "path", "description": "Game group name", "required": true, "type": "string"}, "filterId": {"name": "filterId", "in": "path", "description": "Filter ID", "required": true, "type": "string"}, "gameCategoryInPath": {"name": "gameCategoryId", "in": "path", "description": "Game category public id", "required": true, "type": "string"}, "gameCategoryType": {"name": "type", "in": "query", "description": "Game category type, by default general is used", "required": false, "type": "string", "enum": ["general", "gamestore"]}, "auditOperation__contains": {"name": "operation__contains", "in": "query", "description": "Audit history operation contains", "required": false, "type": "string"}, "lobbyId": {"name": "lobbyId", "in": "path", "description": "lobby's public id", "required": true, "type": "string"}, "terminalId": {"name": "terminalId", "in": "path", "description": "terminal's public id", "required": true, "type": "string"}, "promoId": {"name": "promoId", "in": "path", "description": "Promotion public id", "required": true, "type": "string"}, "bonusId": {"name": "bonusId", "in": "path", "description": "Player bonus id", "required": true, "type": "string"}, "playerBonusType": {"name": "playerBonusType", "in": "query", "description": "Player bonus type", "required": false, "type": "string", "enum": ["freebet"]}, "promoState__in": {"name": "state__in", "in": "query", "description": "List of promotion states. Can be in_progress, pending, finished, expired", "required": false, "type": "string"}, "promoStatus": {"name": "status", "in": "query", "description": "If specified, gets promotions with given status. Can be active or inactive", "required": false, "type": "string"}, "promoArchived": {"name": "archived", "in": "query", "description": "If specified, gets promotions with given archived value", "required": false, "default": false, "type": "boolean"}, "withBalance": {"name": "withBalance", "in": "query", "description": "If specified, gets balances in csv format", "required": false, "default": false, "type": "boolean"}, "promoEnabled": {"name": "enabled", "in": "query", "description": "If specified, gets promotions with given enabled state", "required": false, "type": "boolean"}, "includePromoTotals": {"name": "includeTotals", "in": "query", "description": "If true, adds total participated and total payout fields to response", "required": false, "type": "boolean"}, "includePromoGames": {"name": "includeGames", "in": "query", "description": "If true, adds games list", "required": false, "type": "boolean"}, "promoTitle__contains": {"in": "query", "name": "title__contains", "description": "promo title contains string", "required": false, "type": "string"}, "promoType": {"name": "type", "in": "query", "description": "Type of promo to fetch", "required": false, "type": "string", "enum": ["freebet", "freebet_simple", "bonus_coin"]}, "promoExternalId": {"name": "externalId", "in": "query", "description": "If specified, gets promotions with given external promo id.", "required": false, "type": "string"}, "entityInfoType": {"name": "type", "in": "path", "description": "Type of entity additional info", "required": true, "type": "string"}, "jurisdictionId": {"name": "jurisdictionId", "in": "path", "description": "Jurisdiction public id", "required": true, "type": "string"}, "jurisdictionCode": {"name": "jurisdictionCode", "in": "path", "description": "Jurisdiction code", "required": true, "type": "string"}, "domainId": {"name": "domainId", "in": "path", "description": "Domain public id", "required": true, "type": "string"}, "poolId": {"name": "poolId", "in": "path", "description": "Domain pool public id", "required": true, "type": "string"}, "schemaDefinitionId": {"name": "schemaDefinitionId", "in": "path", "description": "Id of schema definition", "required": true, "type": "string"}, "schemaDefinitionIdInQuery": {"name": "schemaDefinitionId", "in": "query", "description": "Id of schema definition for search", "required": false, "type": "string"}, "gameGroupNameInQuery": {"in": "query", "name": "gameGroupName", "description": "Name of game group for search", "required": false, "type": "string"}, "gameGroupIdInQuery": {"in": "query", "name": "gameGroupId", "description": "Id of game group for search", "required": false, "type": "string"}, "gameLimitsConfigurationId": {"name": "gameLimitsConfigurationId", "in": "path", "description": "Id of game limits configuration", "required": true, "type": "string"}, "segmentIdInQuery": {"in": "query", "name": "segmentId", "description": "The segmentId - specific for POP", "required": false, "type": "number"}, "startDate__gte": {"in": "query", "name": "startDate__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startDate__lte": {"in": "query", "name": "startDate__lte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__gte": {"in": "query", "name": "endDate__gte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__lte": {"in": "query", "name": "endDate__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "playmode": {"name": "playmode", "in": "query", "description": "playmode name \"fun\" | \"bns\" | \"real\" (by default)", "required": false, "type": "string"}, "ticket": {"name": "ticket", "in": "query", "type": "string", "required": false, "description": "merchant player's ticket"}, "username": {"name": "username", "in": "path", "description": "User name", "required": true, "type": "string"}, "gsSettingsName": {"name": "name", "in": "path", "description": "GS settings name", "required": true, "type": "string"}, "country": {"name": "country", "in": "path", "description": "Country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "required": true, "type": "string"}, "language": {"name": "language", "in": "path", "description": "Language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "required": true, "type": "string", "pattern": "^[a-z]{2}$"}, "merchantType": {"name": "merchantType", "in": "path", "description": "Merchant type (ex: ipm, pop)", "required": true, "type": "string"}, "languageInQuery": {"name": "language", "in": "query", "description": "Language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "required": false, "type": "string", "pattern": "^[a-z]{2}(-[a-z]{2})?$"}, "merchantLoginUrl": {"name": "merchantLoginUrl", "in": "query", "description": "merchant login url for integration with IPM only", "required": false, "type": "string"}, "cashier": {"name": "cashier", "in": "query", "description": "cashier site", "required": false, "type": "string"}, "lobby": {"name": "lobby", "in": "query", "description": "lobby site", "required": false, "type": "string"}, "currency": {"name": "currency", "in": "path", "description": "Currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "required": true, "type": "string"}, "transactionId": {"name": "transactionId", "in": "path", "description": "Original transaction Id", "type": "string", "required": true}, "jpCurrencyOptionalInQuery": {"name": "jpCurrency", "in": "query", "description": "Jackpot currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "required": false, "type": "string"}, "jpCurrencyRequiredInQuery": {"name": "jpCurrency", "in": "query", "description": "Jackpot currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "required": true, "type": "string"}, "requiredPlayerCodeStrictEquality": {"in": "query", "name": "playerCode", "description": "player code equal to value", "required": true, "type": "string"}, "requiredGameCodeStrictEquality": {"in": "query", "name": "gameCode", "description": "game code equal to value", "required": true, "type": "string"}, "amount": {"name": "amount", "in": "path", "description": "Money amount", "required": true, "type": "number"}, "notificationId": {"name": "notificationId", "in": "path", "description": "Notification's public id", "required": true, "type": "string"}, "roundId": {"name": "roundId", "in": "path", "description": "Round public id", "required": true, "type": "string"}, "withDetails": {"name": "withDetails", "in": "query", "description": "Includes spin details (It will be false if format CSV)", "required": false, "type": "boolean"}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "isPublicChatBlock": {"name": "isPublicChatBlock", "in": "query", "description": "if true retrun players with block public chat", "required": false, "type": "boolean"}, "isPrivateChatBlock": {"name": "isPrivateChatBlock", "in": "query", "description": "if true return players with block private chat", "required": false, "type": "boolean"}, "hasWarn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "if true return players that has warn in chat", "required": false, "type": "boolean"}, "includeGamesAmount": {"name": "includeGamesAmount", "in": "query", "description": "Include games count available for entity in game category", "required": false, "type": "boolean"}, "includeGames": {"name": "includeGames", "in": "query", "description": "Include games available for entity in game category", "required": false, "type": "boolean"}, "isPayment": {"name": "isPayment", "in": "query", "description": "True if returned entry should be payment", "required": false, "type": "boolean"}, "from": {"name": "from", "in": "query", "description": "Period start date in UNIX-time (seconds that have elapsed since 00:00:00 1 January 1970 (UTC)). This parameter is outdated, please use ts__gte with other date format", "required": false, "type": "integer"}, "to": {"name": "to", "in": "query", "description": "Period end date in UNIX-time (seconds that have elapsed since 00:00:00 1 January 1970 (UTC)). This parameter is outdated, please use ts__lte with other date format", "required": false, "type": "integer"}, "jackpots": {"in": "query", "name": "jackpots", "description": "Append jackpots info to game info", "required": false, "type": "boolean"}, "includeLive": {"in": "query", "name": "includeLive", "description": "Append live info to game info", "required": false, "type": "boolean"}, "username__equal": {"in": "query", "name": "username", "description": "username equal to value", "required": false, "type": "string"}, "username__contains": {"in": "query", "name": "username__contains", "description": "username contains string", "required": false, "type": "string"}, "username__contains!": {"in": "query", "name": "username__contains!", "description": "username doesn't contain string", "required": false, "type": "string"}, "username__in": {"in": "query", "name": "username__in", "description": "list of usernames separated by commas", "required": false, "type": "string"}, "url__equal": {"in": "query", "name": "url", "description": "domain value", "required": false, "type": "string"}, "url__contains": {"in": "query", "name": "url__contains", "description": "url contains string", "required": false, "type": "string"}, "url__contains!": {"in": "query", "name": "url__contains!", "description": "url doesn't contain string", "required": false, "type": "string"}, "url__in": {"in": "query", "name": "url__in", "description": "list of urls separated by commas", "required": false, "type": "string"}, "firstNameStrictEquality": {"in": "query", "name": "firstName", "description": "firstName equal to value", "required": false, "type": "string"}, "firstNameContains": {"in": "query", "name": "firstName__contains", "description": "firstName contains string", "required": false, "type": "string"}, "firstNameNotContains": {"in": "query", "name": "firstName__contains!", "description": "firstName doesn't contain string", "required": false, "type": "string"}, "firstNameIn": {"in": "query", "name": "firstName__in", "description": "list of firstNames separated by commas", "required": false, "type": "string"}, "lastNameStrictEquality": {"in": "query", "name": "lastName", "description": "lastName equal to value", "required": false, "type": "string"}, "lastNameContains": {"in": "query", "name": "lastName__contains", "description": "lastName contains string", "required": false, "type": "string"}, "lastNameNotContains": {"in": "query", "name": "lastName__contains!", "description": "lastName doesn't contain string", "required": false, "type": "string"}, "lastNameIn": {"in": "query", "name": "lastName__in", "description": "list of lastNames separated by commas", "required": false, "type": "string"}, "emailStrictEquality": {"in": "query", "name": "email", "description": "email equal to value", "required": false, "type": "string"}, "emailContains": {"in": "query", "name": "email__contains", "description": "email contains string", "required": false, "type": "string"}, "emailNotContains": {"in": "query", "name": "email__contains!", "description": "email doesn't contain string", "required": false, "type": "string"}, "emailIn": {"in": "query", "name": "email__in", "description": "list of emails separated by commas", "required": false, "type": "string"}, "gameGroupStrictEquality": {"in": "query", "name": "gameGroup", "description": "game group equal to value", "required": false, "type": "string"}, "gameGroupName": {"in": "query", "name": "gameGroup", "description": "game group name", "required": false, "type": "string"}, "gameGroupId": {"in": "query", "name": "gameGroupId", "description": "game group id", "required": false, "type": "string"}, "gameGroupIn": {"in": "query", "name": "gameGroup__in", "description": "game groups separated by commas", "required": false, "type": "string"}, "currencyStrictEquality": {"in": "query", "name": "currency", "description": "currency equal to value", "required": false, "type": "string"}, "currencyIn": {"in": "query", "name": "currency__in", "description": "currencies separated by commas", "required": false, "type": "string"}, "countryStrictEquality": {"in": "query", "name": "country", "description": "country equal to value", "required": false, "type": "string"}, "countryIn": {"in": "query", "name": "country__in", "description": "countries separated by commas", "required": false, "type": "string"}, "withoutGameGroup": {"in": "query", "name": "withoutGameGroup", "description": "true when player doesn't belong to game group", "required": false, "type": "boolean"}, "lastLogin": {"in": "query", "name": "lastLogin", "description": "sharp time in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "lastLogin__gt": {"in": "query", "name": "lastLogin__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "lastLogin__lt": {"in": "query", "name": "lastLogin__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "lastLogin__gte": {"in": "query", "name": "lastLogin__gte", "description": "Last login from in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "lastLogin__lte": {"in": "query", "name": "lastLogin__lte", "description": "Last login to in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "expired__gte": {"in": "query", "name": "expired__gte", "description": "Expired date from in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "expired__lte": {"in": "query", "name": "expired__lte", "description": "Expired date to in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt": {"in": "query", "name": "createdAt", "description": "sharp time in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__gt": {"in": "query", "name": "createdAt__gt", "description": "date in ISO 8601 timestamp greater than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__gte": {"in": "query", "name": "createdAt__gte", "description": "date in ISO 8601 timestamp greater than or equal(e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__lt": {"in": "query", "name": "createdAt__lt", "description": "date in ISO 8601 timestamp less than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__lte": {"in": "query", "name": "createdAt__lte", "description": "date in ISO 8601 timestamp less than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt": {"in": "query", "name": "updatedAt", "description": "date in ISO 8601 timestamp greater than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__gt": {"in": "query", "name": "updatedAt__gt", "description": "date in ISO 8601 timestamp greater than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__gte": {"in": "query", "name": "updatedAt__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__lt": {"in": "query", "name": "updatedAt__lt", "description": "date in ISO 8601 timestamp less than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__lte": {"in": "query", "name": "updatedAt__lte", "description": "date in ISO 8601 timestamp less than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "affiliateCodeStyleStrictEquality": {"in": "query", "name": "affiliateCode", "description": "affiliate code equal to value", "required": false, "type": "string"}, "affiliateCodeContains": {"in": "query", "name": "affiliateCode__contains", "description": "affiliate code contains string", "required": false, "type": "string"}, "affiliateCodeNotContains": {"in": "query", "name": "affiliateCode__contains!", "description": "affiliate code doesn't contain string", "required": false, "type": "string"}, "affiliateCodeIn": {"in": "query", "name": "affiliateCode__in", "description": "list of affiliate codes separated by commas", "required": false, "type": "string"}, "playerCodeStrictEquality": {"in": "query", "name": "playerCode", "description": "player code equal to value", "required": false, "type": "string"}, "playerCodeRequiredInQuery": {"in": "query", "name": "playerCode", "description": "player code equal to value", "required": true, "type": "string"}, "playerCodeContains": {"in": "query", "name": "playerCode__contains", "description": "player code contains string", "required": false, "type": "string"}, "playerCodeNotContains": {"in": "query", "name": "playerCode__contains!", "description": "player code doesn't contain string", "required": false, "type": "string"}, "playerCodeIn": {"in": "query", "name": "playerCode__in", "description": "list of player codes separated by commas", "required": false, "type": "string"}, "codeOfPlayerStrictEquality": {"in": "query", "name": "code", "description": "player code equal to value", "required": false, "type": "string"}, "codeOfPlayerContains": {"in": "query", "name": "code__contains", "description": "player code contains string", "required": false, "type": "string"}, "codeOfPlayerNotContains": {"in": "query", "name": "code__contains!", "description": "player code doesn't contain string", "required": false, "type": "string"}, "codeOfPlayerIn": {"in": "query", "name": "code__in", "description": "list of player codes separated by commas", "required": false, "type": "string"}, "promosIdIn": {"in": "query", "name": "promosId__in", "description": "list of promosIds separated by commas", "required": false, "type": "string"}, "gameCodeStrictEquality": {"in": "query", "name": "gameCode", "description": "game code equal to value", "required": false, "type": "string"}, "unfinishedRoundStatus": {"in": "query", "name": "status", "description": "status of unfinished round", "type": "string", "required": false, "enum": ["broken", "unfinished", "requireLogout"]}, "includeBrokenSpin": {"in": "query", "name": "includeBrokenSpin", "description": "true to include broken spin data as well", "type": "boolean", "required": false}, "withTrx": {"in": "query", "name": "withTrx", "description": "true to include spin transaction Id", "type": "boolean", "required": false}, "gameContextId": {"in": "query", "name": "gameContextId", "description": "gameContextId", "type": "string", "required": false}, "gameContextIdRequiredInQuery": {"in": "query", "name": "gameContextId", "description": "gameContextId", "type": "string", "required": true}, "forceFlagInQuery": {"in": "query", "name": "force", "description": "Force to perform operation", "required": false, "type": "boolean"}, "closeInSWWalletOnlyFlagInQuery": {"in": "query", "name": "closeInSWWalletOnly", "description": "Close round in SW wallet", "required": false, "type": "boolean"}, "ignoreMerchantParamsFlagInQuery": {"in": "query", "name": "ignoreMerchantParams", "description": "Finalize round ignoring the supportForceFinishAndRevert flag of the merchant", "required": false, "type": "boolean"}, "roundIdInQuery": {"in": "query", "name": "roundId", "description": "public round id", "required": false, "type": "string"}, "gameCodeIn": {"in": "query", "name": "gameCode__in", "description": "list of game codes separated by commas", "required": false, "type": "string"}, "roundIdStrictEquality": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundIdIn": {"in": "query", "name": "roundId__in", "description": "list of roundIds separated by commas", "required": false, "type": "string"}, "domainStrictEquality": {"in": "query", "name": "domain", "description": "domain equal to value", "required": false, "type": "string"}, "domainContains": {"in": "query", "name": "domain__contains", "description": "domain contains string", "required": false, "type": "string"}, "domainNotContains": {"in": "query", "name": "domain__contains!", "description": "domain doesn't contain string", "required": false, "type": "string"}, "domainIn": {"in": "query", "name": "domain__in", "description": "list of domains separated by commas", "required": false, "type": "string"}, "status": {"in": "query", "name": "status", "type": "string", "required": false, "description": "suspended or normal (by default)", "enum": ["normal", "suspended"]}, "entityStatus": {"in": "body", "name": "status", "schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Available statuses for entity", "example": "normal", "enum": ["normal", "suspended", "maintenance", "blocked_by_admin", "test"]}}}, "required": true}, "isTest": {"in": "query", "name": "isTest", "type": "boolean", "required": false, "description": "is test entry", "enum": [true, false]}, "toEntityId": {"in": "query", "name": "toEntityId", "description": "brand id operation was made to", "required": false, "type": "string"}, "fromEntityId": {"in": "query", "name": "fromEntityId", "description": "brand id operation was made to", "required": false, "type": "string"}, "debitOrCredit": {"in": "query", "name": "type", "type": "string", "required": false, "description": "states that only debit or credit operations must be fetched", "enum": ["debit", "credit"]}, "labelGroupName": {"in": "query", "name": "group", "type": "string", "required": false, "description": "label group name"}, "labelGroupType": {"in": "query", "name": "type", "type": "string", "required": false, "description": "label group; either game or entity", "enum": ["game", "entity", "promotion"]}, "labelGroupRelationType": {"in": "query", "name": "relationType", "type": "string", "required": false, "description": "label group; either o: one to one or m: one to many.", "enum": ["o", "m"]}, "labelTitle__contains": {"in": "query", "name": "title__contains", "type": "string", "required": false, "description": "label title contains"}, "unread": {"in": "query", "name": "unread", "type": "boolean", "required": false, "description": "is message unread", "enum": [true, false]}, "agentGroupStatus": {"name": "agentGroupStatus", "description": "New status and list of domains for updating", "required": true, "in": "body", "schema": {"$ref": "#/definitions/AgentGroupStatus"}}, "labelsInfo": {"name": "labels", "description": "Label info", "required": true, "in": "body", "schema": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}}, "biReportDomainsCreateData": {"name": "body", "description": "Data for creating Business Intelligence report domains", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiReportDomainsCreateData"}}, "biReportDomainsUpdateData": {"name": "body", "description": "Data for updating Business Intelligence report domains", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiReportDomainsUpdateData"}}, "biReportCreateData": {"name": "body", "description": "Data for creating Business Intelligence report", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiReportCreateData"}}, "biReportUpdateData": {"name": "body", "description": "Data for updating Business Intelligence report", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiReportUpdateData"}}, "biCreateUrlData": {"name": "body", "description": "Data for generating token and URL for Business Intelligence report", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiCreateUrlData"}}, "biReportUpdateOrderingData": {"name": "body", "description": "Data for updating Business Intelligence report ordering", "required": true, "in": "body", "schema": {"$ref": "#/definitions/BiReportUpdateOrderingData"}}, "whitelist": {"name": "whitelist", "description": "IP address list", "required": true, "in": "body", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "playerGroupStatus": {"name": "playerGroupStatus", "description": "New status and list of players for updating", "required": true, "in": "body", "schema": {"$ref": "#/definitions/PlayerGroupStatus"}}, "gameGroupStatus": {"name": "gameGroupStatus", "description": "New status and list of game for updating", "required": true, "in": "body", "schema": {"$ref": "#/definitions/GameGroupStatus"}}, "gameGroupLimitFilters": {"name": "gameGroupLimitFilters", "description": "New limitFilters and list of game for updating", "required": true, "in": "body", "schema": {"$ref": "#/definitions/GameGroupLimitFilters"}}, "userGroupStatus": {"name": "userGroupStatus", "description": "New status and list of users for updating", "required": true, "in": "body", "schema": {"$ref": "#/definitions/UserGroupStatus"}}, "externalReference": {"name": "externalReference", "description": "External reference data", "required": false, "in": "body", "schema": {"$ref": "#/definitions/ExternalReference"}}, "queryFinished": {"name": "finished", "in": "query", "description": "Get finished/unfinished rounds. true - finished, false - unfinished, undefined - all", "required": false, "type": "boolean"}, "extRecoveryType": {"name": "recoveryType", "in": "query", "description": "Get rounds by recovery type: finalize / force-finish / null", "required": false, "type": "string", "enum": ["finalize", "force-finish", "null"]}, "queryFormat": {"name": "format", "in": "query", "description": "Desired format", "required": false, "type": "string", "enum": ["csv"]}, "onlyOwnSettings": {"name": "onlyOwnSettings", "in": "query", "description": "Set to true if only own settings should be fetched (not merged with parent ones)", "required": false, "type": "boolean"}, "extendSettings": {"name": "extendSettings", "in": "query", "description": "Set to true if settings should be extended with divided settings (own and parent settings)", "required": false, "type": "boolean"}, "createEntity": {"name": "info", "in": "body", "description": "Data for creating entity or brand", "required": true, "schema": {"$ref": "#/definitions/CreateEntityData"}}, "updateEntity": {"name": "info", "in": "body", "description": "Data for update entity or brand", "required": true, "schema": {"$ref": "#/definitions/UpdateEntityData"}}, "createBrandEntity": {"name": "info", "in": "body", "description": "Data for creating merchant entity", "required": true, "schema": {"$ref": "#/definitions/CreateBrandEntityData"}}, "createMerchantEntity": {"name": "info", "in": "body", "description": "Data for creating merchant entity", "required": true, "schema": {"$ref": "#/definitions/CreateMerchantEntityData"}}, "updateMerchantEntity": {"name": "info", "in": "body", "description": "Data for update merchant entity", "required": true, "schema": {"$ref": "#/definitions/UpdateMerchantEntityData"}}, "createPlayer": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreatePlayerData"}}, "upsertPlayerInfo": {"name": "info", "in": "body", "schema": {"$ref": "#/definitions/UpsertPlayerInfoData"}}, "upsertPlayerInfoStudioUser": {"name": "info", "in": "body", "schema": {"$ref": "#/definitions/UpsertPlayerInfoDataStudioUser"}}, "updatePlayerPassword": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdatePlayerPasswordData"}}, "registerPlayer": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterPlayerData"}}, "updatePlayer": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdatePlayerData"}}, "createUser": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateUserData"}}, "createGameServerSettings": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameServerSettingsData"}}, "updateGameServerSettings": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameServerSettingsUpdateData"}}, "changeEntityGame": {"name": "info", "in": "body", "description": "Entity game info", "required": true, "schema": {"$ref": "#/definitions/ChangeEntityGameData"}}, "gameCodes": {"name": "info", "in": "body", "description": "Codes of games", "required": true, "schema": {"$ref": "#/definitions/GameCodesData"}}, "EntityAdditionalInfoCreateRecord": {"name": "info", "in": "body", "description": "Any additional information for an entity", "required": true, "schema": {"type": "object", "example": {"lowBalance": {"emails": ["<EMAIL>"], "currencies": {"USD": {"min": 100}}}, "promotions": {"emails": ["<EMAIL>"]}}}}, "countries__body": {"name": "info", "in": "body", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "required": true, "schema": {"$ref": "#/definitions/CountriesArray"}}, "currencies__body": {"name": "info", "in": "body", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "required": true, "schema": {"$ref": "#/definitions/CurrenciesArray"}}, "languages__body": {"name": "info", "in": "body", "description": "list of available language codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "required": true, "schema": {"$ref": "#/definitions/LanguagesArray"}}, "auditId": {"in": "query", "name": "auditId", "description": "auditId equal to value", "required": false, "type": "string"}, "auditId__in": {"in": "query", "name": "auditId__in", "description": "list of auditIds separated by commas", "required": false, "type": "string"}, "includeSubEntities": {"in": "query", "name": "includeSubEntities", "description": "list of audits not including this auditType", "required": false, "type": "boolean"}, "initiatorType": {"in": "query", "name": "initiatorType", "description": "initiatorType equal to value (enum)", "required": false, "type": "string", "enum": ["user", "player", "system"]}, "initiatorServiceName": {"in": "query", "name": "initiatorServiceName", "description": "initiatorServiceName equal to service value", "required": false, "type": "string"}, "initiatorName": {"in": "query", "name": "initiatorName", "description": "initiatorName equal to value", "required": false, "type": "string"}, "initiatorName__contains": {"in": "query", "name": "initiatorName__contains", "description": "initiatorName contains string", "required": false, "type": "string"}, "initiatorName__contains!": {"in": "query", "name": "initiatorName__contains!", "description": "initiatorName doesn't contain string", "required": false, "type": "string"}, "initiatorName__in": {"in": "query", "name": "initiatorName__in", "description": "list of initiator<PERSON>ames separated by commas", "required": false, "type": "string"}, "ip": {"in": "query", "name": "ip", "description": "ip address", "required": false, "type": "string"}, "ip__contains": {"in": "query", "name": "ip__contains", "description": "ip contains string", "required": false, "type": "string"}, "ip__contains!": {"in": "query", "name": "ip__contains!", "description": "ip doesn't contain string", "required": false, "type": "string"}, "ip__in": {"in": "query", "name": "ip__in", "description": "list of ips separated by commas", "required": false, "type": "string"}, "userAgent": {"in": "query", "name": "userAgent", "description": "userAgent equal to value", "required": false, "type": "string"}, "userAgent__contains": {"in": "query", "name": "userAgent__contains", "description": "userAgent contains string", "required": false, "type": "string"}, "userAgent__contains!": {"in": "query", "name": "userAgent__contains!", "description": "userAgent doesn't contain string", "required": false, "type": "string"}, "userAgent__in": {"in": "query", "name": "userAgent__in", "description": "list of userAgents separated by commas", "required": false, "type": "string"}, "initiatorIssueId": {"in": "query", "name": "initiatorIssueId", "description": "Jira issue equal to value", "required": false, "type": "string"}, "initiatorIssueId__contains": {"in": "query", "name": "initiatorIssueId__contains", "description": "Jira issue contains string", "required": false, "type": "string"}, "initiatorIssueId__contains!": {"in": "query", "name": "initiatorIssueId__contains!", "description": "Jira issue doesn't contain string", "required": false, "type": "string"}, "initiatorIssueId__in": {"in": "query", "name": "initiatorIssueId__in", "description": "list of Jira issues separated by commas", "required": false, "type": "string"}, "bet": {"in": "query", "name": "bet", "description": "sharp value for bet", "required": false, "type": "number"}, "bet__gt": {"in": "query", "name": "bet__gt", "description": "bet is greater-than", "required": false, "type": "number"}, "bet__lt": {"in": "query", "name": "bet__lt", "description": "bet is less-than", "required": false, "type": "number"}, "bet__gte": {"in": "query", "name": "bet__gte", "description": "bet is greater-than-or-equal", "required": false, "type": "number"}, "bet__lte": {"in": "query", "name": "bet__lte", "description": "bet is less-than-or-equal", "required": false, "type": "number"}, "win": {"in": "query", "name": "win", "description": "sharp value for win", "required": false, "type": "number"}, "win__gt": {"in": "query", "name": "win__gt", "description": "win is greater-than", "required": false, "type": "number"}, "win__lt": {"in": "query", "name": "win__lt", "description": "win is less-than", "required": false, "type": "number"}, "win__gte": {"in": "query", "name": "win__gte", "description": "win is greater-than-or-equal", "required": false, "type": "number"}, "win__lte": {"in": "query", "name": "win__lte", "description": "win is less-than-or-equal", "required": false, "type": "number"}, "revenue": {"in": "query", "name": "revenue", "description": "sharp value for revenue", "required": false, "type": "number"}, "revenue__gt": {"in": "query", "name": "revenue__gt", "description": "revenue is greater-than", "required": false, "type": "number"}, "revenue__lt": {"in": "query", "name": "revenue__lt", "description": "revenue is less-than", "required": false, "type": "number"}, "revenue__gte": {"in": "query", "name": "revenue__gte", "description": "revenue is greater-than-or-equal", "required": false, "type": "number"}, "revenue__lte": {"in": "query", "name": "revenue__lte", "description": "revenue is less-than-or-equal", "required": false, "type": "number"}, "device": {"in": "query", "name": "device", "description": "code of player's device", "required": false, "type": "string"}, "balanceBefore": {"in": "query", "name": "balanceBefore", "description": "balance before round of revenue", "required": false, "type": "number"}, "balanceBefore__gt": {"in": "query", "name": "balanceBefore__gt", "description": "balance before round is greater-than", "required": false, "type": "number"}, "balanceBefore__lt": {"in": "query", "name": "balanceBefore__lt", "description": "balance before round is less-than", "required": false, "type": "number"}, "balanceBefore__gte": {"in": "query", "name": "balanceBefore__gte", "description": "balance before round is greater-than-or-equal", "required": false, "type": "number"}, "balanceBefore__lte": {"in": "query", "name": "balanceBefore__lte", "description": "balance before round is less-than-or-equal", "required": false, "type": "number"}, "balanceAfter": {"in": "query", "name": "balanceAfter", "description": "balance after round of revenue", "required": false, "type": "number"}, "balanceAfter__gt": {"in": "query", "name": "balanceAfter__gt", "description": "balance after round is greater-than", "required": false, "type": "number"}, "balanceAfter__lt": {"in": "query", "name": "balanceAfter__lt", "description": "balance after round is less-than", "required": false, "type": "number"}, "balanceAfter__gte": {"in": "query", "name": "balanceAfter__gte", "description": "balance after round is greater-than-or-equal", "required": false, "type": "number"}, "balanceAfter__lte": {"in": "query", "name": "balanceAfter__lte", "description": "balance after round is less-than-or-equal", "required": false, "type": "number"}, "ts": {"in": "query", "name": "ts", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gte": {"in": "query", "name": "ts__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lte": {"in": "query", "name": "ts__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gt": {"in": "query", "name": "ts__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lt": {"in": "query", "name": "ts__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs": {"in": "query", "name": "firstTs", "description": "sharp first timestamp of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gt": {"in": "query", "name": "firstTs__gt", "description": "first timestamp greater than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lt": {"in": "query", "name": "firstTs__lt", "description": "first timestamp less than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gte": {"in": "query", "name": "firstTs__gte", "description": "first timestamp greater than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lte": {"in": "query", "name": "firstTs__lte", "description": "first timestamp less than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "entity__equal": {"in": "query", "name": "entity", "description": "equal to value", "required": false, "type": "string"}, "entity__contains": {"in": "query", "name": "entity__contains", "description": "contains string", "required": false, "type": "string"}, "entity__contains!": {"in": "query", "name": "entity__contains!", "description": "doesn't contain string", "required": false, "type": "string"}, "entity__in": {"in": "query", "name": "entity__in", "description": "list, separated by commas", "required": false, "type": "string"}, "amount_strict": {"in": "query", "name": "amount", "description": "sharp value for amount", "required": false, "type": "number"}, "amount__gt": {"in": "query", "name": "amount__gt", "description": "amount is greater-than", "required": false, "type": "number"}, "amount__lt": {"in": "query", "name": "amount__lt", "description": "amount is less-than", "required": false, "type": "number"}, "amount__gte": {"in": "query", "name": "amount__gte", "description": "amount is greater-than-or-equal", "required": false, "type": "number"}, "amount__lte": {"in": "query", "name": "amount__lte", "description": "amount is less-than-or-equal", "required": false, "type": "number"}, "paymentMethodCodeStrictEquality": {"in": "query", "name": "paymentMethodCode", "description": "payment method code equal to value", "required": false, "type": "string"}, "trxId": {"in": "query", "name": "trxId", "description": "transaction id", "required": false, "type": "string"}, "extTrxId": {"in": "query", "name": "extTrxId", "description": "external reference", "required": false, "type": "string"}, "paymentMethodCodeIn": {"in": "query", "name": "paymentMethodCode__in", "description": "payment method codes separated by commas", "required": false, "type": "string"}, "orderTypeIn": {"in": "query", "name": "orderType__in", "description": "comma separated order types", "required": false, "type": "string"}, "startDate": {"in": "query", "name": "startDate", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startDate__gt": {"in": "query", "name": "startDate__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startDate__lt": {"in": "query", "name": "startDate__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate": {"in": "query", "name": "endDate", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__gt": {"in": "query", "name": "endDate__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__lt": {"in": "query", "name": "endDate__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "playedGames__gt": {"in": "query", "name": "playedGames__gt", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__lt": {"in": "query", "name": "playedGames__lt", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__gte": {"in": "query", "name": "playedGames__gte", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__lte": {"in": "query", "name": "playedGames__lte", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames": {"in": "query", "name": "playedGames", "description": "Amount of played games", "required": false, "type": "string"}, "totalBets__gt": {"in": "query", "name": "totalBets__gt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__lt": {"in": "query", "name": "totalBets__lt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__gte": {"in": "query", "name": "totalBets__gte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__lte": {"in": "query", "name": "totalBets__lte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets": {"in": "query", "name": "totalBets", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__gt": {"in": "query", "name": "totalWins__gt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__lt": {"in": "query", "name": "totalWins__lt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__gte": {"in": "query", "name": "totalWins__gte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__lte": {"in": "query", "name": "totalWins__lte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins": {"in": "query", "name": "totalWins", "description": "Total sum of players bets", "required": false, "type": "string"}, "paymentDate": {"in": "query", "name": "paymentDate", "description": "Payment date in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__gt": {"in": "query", "name": "paymentDate__gt", "description": "Payment date greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__lt": {"in": "query", "name": "paymentDate__lt", "description": "Payment date lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__gte": {"in": "query", "name": "paymentDate__gte", "description": "Payment date greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__lte": {"in": "query", "name": "paymentDate__lte", "description": "Payment date lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour": {"in": "query", "name": "paymentDateHour", "description": "Payment time truncate to hour in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__gt": {"in": "query", "name": "paymentDateHour__gt", "description": "Payment time truncate to hour greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__lt": {"in": "query", "name": "paymentDateHour__lt", "description": "Payment time truncate to hour lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__gte": {"in": "query", "name": "paymentDateHour__gte", "description": "Payment time truncate to hour greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__lte": {"in": "query", "name": "paymentDateHour__lte", "description": "Payment time truncate to hour lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "transferData": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TransferDataInfo"}}, "orderStatus": {"in": "query", "name": "orderStatus", "type": "string", "required": false, "description": "declined or approved (by default)", "enum": ["approved", "declined", "init", "blocked"]}, "gameCategoryId": {"in": "query", "name": "gamecategoryId", "description": "Game category public id", "required": false, "type": "string"}, "gameProviderId": {"in": "query", "name": "providerId", "description": "Game provider id", "required": false, "type": "string"}, "gameProviderCodeEquality": {"in": "query", "name": "providerCode", "description": "Game provider code equals to value", "required": false, "type": "string"}, "gameProviderCode__in": {"in": "query", "name": "providerCode__in", "description": "Game provider codes separated by commas", "required": false, "type": "string"}, "gameProviderTitleEquality": {"in": "query", "name": "providerTitle", "description": "Game provider title equals to value", "required": false, "type": "string"}, "gameProviderTitleContains": {"in": "query", "name": "providerTitle__contains", "description": "Game provider title contains string", "required": false, "type": "string"}, "gameProviderTitleNotContains": {"in": "query", "name": "providerTitle__contains!", "description": "Game provider title not contains string", "required": false, "type": "string"}, "codeStrictEquality": {"in": "query", "name": "code", "description": "code equals to value", "required": false, "type": "string"}, "code__in": {"in": "query", "name": "code__in", "description": "codes separated by commas", "required": false, "type": "string"}, "searchTitleCode": {"in": "query", "name": "search", "description": "filter Terminals by title or player code", "required": false, "type": "string"}, "titleStrictEquality": {"in": "query", "name": "title", "description": "title equals to value", "required": false, "type": "string"}, "operatorSiteGroupNameStrictEquality": {"in": "query", "name": "operatorSiteGroupName", "description": "operator site group name equals to value", "required": false, "type": "string"}, "externalCodeStrictEquality": {"in": "query", "name": "externalCode", "description": "External code equals to value", "required": false, "type": "string"}, "titleContains": {"in": "query", "name": "title__contains", "description": "title contains string", "required": false, "type": "string"}, "typeStrictEquality": {"in": "query", "name": "type", "description": "type equals to value", "required": false, "type": "string", "enum": ["action", "slot", "table", "external", "live"]}, "dateHour": {"in": "query", "name": "dateHour", "description": "JP time truncate to hour in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__gt": {"in": "query", "name": "dateHour__gt", "description": "JP time truncate to hour greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__lt": {"in": "query", "name": "dateHour__lt", "description": "JP time truncate to hour lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__gte": {"in": "query", "name": "dateHour__gte", "description": "JP time truncate to hour greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__lte": {"in": "query", "name": "dateHour__lte", "description": "JP time truncate to hour lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "titleNotContains": {"in": "query", "name": "title__contains!", "description": "title not contains string", "required": false, "type": "string"}, "idStrictEquality": {"in": "query", "name": "id", "description": "public id equals to value", "required": false, "type": "string"}, "idIn": {"in": "query", "name": "id__in", "description": "public ids separated by commas", "required": false, "type": "string"}, "labelsIdIn": {"in": "query", "name": "labelsId__in", "description": "label's codes separated by commas, result contains at least one label", "required": false, "type": "string"}, "labelsGroupTypeIn": {"in": "query", "name": "labelsGroupType__in", "description": "label's group separated by commas", "required": false, "type": "string"}, "isFreebetSupported": {"in": "query", "name": "isFreebetSupported", "description": "true if searched games should support freebets", "required": false, "type": "boolean"}, "isCustomLimitsSupported": {"in": "query", "name": "isCustomLimitsSupported", "description": "true if game limits updating for old limits system is allowed, used by BO", "required": false, "type": "boolean"}, "decreaseMaxBetSupported": {"in": "query", "name": "decreaseMaxBetSupported", "description": "true if need to support game group filters to decrease max bet", "required": false, "type": "boolean"}, "increaseMinBetSupported": {"in": "query", "name": "increaseMinBetSupported", "description": "true if need to support game group filters to increase min bet", "required": false, "type": "boolean"}, "limitFiltersWillBeApplied": {"in": "query", "name": "limitFiltersWillBeApplied", "description": "true if game group limit filters will be applied to the game", "required": false, "type": "boolean"}, "endTime__gt": {"in": "query", "name": "endTime__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endTime__lt": {"in": "query", "name": "endTime__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endTime__gte": {"in": "query", "name": "endTime__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endTime__lte": {"in": "query", "name": "endTime__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "isBonusCoinsSupported": {"in": "query", "name": "isBonusCoinsSupported", "description": "true if searched games should support bonus coins", "required": false, "type": "boolean"}, "isMarketplaceSupported": {"in": "query", "name": "isMarketplaceSupported", "description": "true when searched games supports creating game limit configurations via marketplace", "required": false, "type": "boolean"}, "transferEnabled": {"in": "query", "name": "transferEnabled", "description": "true if searched games should support transfer", "required": false, "type": "boolean"}, "isGRCGame": {"in": "query", "name": "isGRCGame", "description": "true if searched games should be GRC", "required": false, "type": "boolean"}, "shortInfo": {"in": "query", "name": "shortInfo", "description": "true if need to return game short info (without limits)", "required": false, "type": "boolean"}, "jackpotTypes": {"in": "query", "name": "jackpotTypes", "description": "'true' or comma-separated list of jackpot types if searched games should have jackpots", "required": false, "type": "string"}, "live": {"in": "query", "name": "live", "description": "'true' or json string of live parameters if searched games should have live streaming", "required": false, "type": "string"}, "lobbyPublicId": {"in": "query", "name": "lobbyId", "description": "lobby public id", "required": false, "type": "string"}, "lobbyPublicIdIn": {"in": "query", "name": "lobbyId__in", "description": "lobby public ids separated by commas", "required": false, "type": "string"}, "gameInformation": {"name": "info", "in": "body", "description": "Create game information request.  Live games can be created through Live Admin only", "required": true, "schema": {"$ref": "#/definitions/GameRegistration"}}, "gameUpdateInformation": {"name": "info", "in": "body", "description": "Update game information", "required": true, "schema": {"$ref": "#/definitions/GameUpdateData"}}, "withAudit": {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "True if audit data for player changes must be fetched", "required": false, "type": "boolean"}, "withLastAction": {"name": "withLastAction", "in": "query", "description": "If true then add last game action date to Player's data, default false", "required": false, "type": "boolean"}, "fullTextSearchQuery": {"name": "q", "in": "query", "description": "Argument of full text search (use with \"fields\"). Case-insensitive.", "required": false, "type": "string"}, "fullTextSearchFields": {"name": "fields", "in": "query", "description": "Comma separated fields (use with \"q\").", "required": false, "type": "string"}, "spinNumber": {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}, "respGamingSettingsType": {"in": "query", "name": "type", "type": "string", "required": false, "description": "type of responsible settings", "enum": ["casino", "sport_bet"]}, "jurisdictionStrictEquality": {"in": "query", "name": "jurisdiction", "description": "player jurisdiction equal to value", "required": false, "type": "string"}, "jurisdictionIn": {"in": "query", "name": "jurisdiction__in", "description": "list of jurisdictions separated by commas", "required": false, "type": "string"}, "isOnTimeout": {"in": "query", "name": "isOnTimeout", "type": "boolean", "required": false, "description": "Player is on timeout", "enum": [false, true]}, "isSelfExcluded": {"in": "query", "name": "isSelfExcluded", "type": "boolean", "required": false, "description": "Player has temporarily excluded himself from system", "enum": [false, true]}, "casinoTimeoutTillDate__gt": {"in": "query", "name": "casinoTimeoutTillDate__gt", "description": "date in ISO 8601 timestamp is greater (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "casinoTimeoutTillDate__gte": {"in": "query", "name": "casinoTimeoutTillDate__gte", "description": "date in ISO 8601 timestamp is greater or equal (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "casinoTimeoutTillDate__lt": {"in": "query", "name": "casinoTimeoutTillDate__lt", "description": "date in ISO 8601 timestamp is less than (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "casinoTimeoutTillDate__lte": {"in": "query", "name": "casinoTimeoutTillDate__lte", "description": "date in ISO 8601 timestamp is less than or equal (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "selfExclusionTillDate__gt": {"in": "query", "name": "selfExclusionTillDate__gt", "description": "date in ISO 8601 timestamp is greater (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "selfExclusionTillDate__gte": {"in": "query", "name": "selfExclusionTillDate__gte", "description": "date in ISO 8601 timestamp is greater or equal (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "selfExclusionTillDate__lt": {"in": "query", "name": "selfExclusionTillDate__lt", "description": "date in ISO 8601 timestamp is less than (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "selfExclusionTillDate__lte": {"in": "query", "name": "selfExclusionTillDate__lte", "description": "date in ISO 8601 timestamp is less than or equal (e.g. 2018-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "respGamingSuspensionType": {"in": "query", "name": "suspensionTypes", "type": "string", "required": false, "description": "type of player exclusion", "enum": ["time-out", "self-exclusion"]}, "respGamingSuspensionType__in": {"in": "query", "name": "suspensionTypes__in", "type": "array", "required": false, "description": "type of player exclusion", "items": {"type": "string", "enum": ["time-out", "self-exclusion"]}}, "languageQuery": {"name": "language", "in": "query", "description": "If specified, visualisation will be translated to given language", "required": false, "type": "string"}, "timezoneQuery": {"name": "timezone", "in": "query", "description": "If specified, visualisation will be converted to given timezone (Asia/Shanghai)", "required": false, "type": "string"}, "setPlayerPassword": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SetPlayerPasswordData"}}, "createSchemaDefinition": {"name": "info", "in": "body", "description": "Data for creating schema definition", "required": true, "schema": {"$ref": "#/definitions/CreateSchemaDefinition"}}, "updateSchemaDefinition": {"name": "info", "in": "body", "description": "Data for editing schema definition", "required": true, "schema": {"$ref": "#/definitions/UpdateSchemaDefinition"}}, "createGameLimitsConfiguration": {"name": "info", "in": "body", "description": "Data for creating schema definition", "required": true, "schema": {"$ref": "#/definitions/CreateGameLimitsConfiguration"}}, "updateGameLimitsConfiguration": {"name": "info", "in": "body", "description": "Data for editing schema definition", "required": true, "schema": {"$ref": "#/definitions/UpdateGameLimitsConfiguration"}}, "gameProviderCode": {"name": "gameProviderCode", "in": "query", "type": "string", "description": "Game Provider Code to search for", "required": false}, "currencyInQuery": {"name": "currency", "in": "query", "type": "string", "description": "currency code to represent result in.", "required": false}, "limitsCurrencyInQuery": {"name": "currency", "in": "query", "type": "string", "description": "currency code for fetching only this currency limits", "required": false}, "playerCodeInQuery": {"name": "playerCode", "in": "query", "type": "string", "description": "Player code to search for", "required": false}, "gameCodeInQuery": {"name": "gameCode", "in": "query", "type": "string", "description": "Gamecode to search for", "required": false}, "gameCodeInQueryRequired": {"name": "gameCode", "in": "query", "type": "string", "description": "Game code", "required": true}, "insertedAt": {"in": "query", "name": "insertedAt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__gt": {"in": "query", "name": "insertedAt__gt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__lt": {"in": "query", "name": "insertedAt__lt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "extTrxIdRequired": {"name": "extTrxId", "in": "query", "type": "string", "description": "External trx id generated by ext game provider to search for", "required": true}, "gameProviderCodeRequired": {"name": "gameProviderCode", "in": "query", "type": "string", "description": "Game Provider Code to search for", "required": true}, "email": {"name": "email", "in": "path", "type": "string", "description": "customer email. must be escaped before usage", "required": true}, "trxDate__gt": {"in": "query", "name": "trxDate__gt", "description": "Trx date truncate to datetime greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": true, "type": "string"}, "features": {"in": "query", "name": "features", "description": "json string of features object of searched games. Example: {\"isGRCGame\":true, \"customNewFeature\": 1, \"live\":{}, \"jackpotTypes\":[], \"transferEnabled\": false}", "required": false, "type": "string"}, "customData": {"in": "query", "name": "customData", "description": "json string of customData object of searched users. Example: {\"dateOfBirth\":2020-12-07T21:00:00.000Z, \"nickname\": \"vasya123\", \"group\":{}, \"role\":{}, \"cardNumber\": 434343432312}", "required": false, "type": "string"}, "owner": {"in": "query", "name": "owner", "required": false, "description": "filter promotions by owner", "type": "string", "enum": ["skywind", "operator"]}, "reason": {"in": "query", "name": "reason", "required": false, "description": "the reason of the suspend action", "type": "string"}, "recoveryType": {"name": "recoveryType", "in": "query", "description": "Recovery type for rounds that were resolved manually", "required": false, "type": "string", "enum": ["revert", "force-finish", "finalize"]}, "recoveryType__in": {"in": "query", "name": "recoveryType__in", "description": "list of recovery types for rounds that were resolved manually", "required": false, "type": "string"}, "schemaConfigurationId": {"name": "schemaConfigurationId", "in": "path", "description": "Id of schema configuration", "required": true, "type": "string"}, "createSchemaConfiguration": {"name": "info", "in": "body", "description": "Data for creating schema configuration", "required": true, "schema": {"$ref": "#/definitions/CreateSchemaConfiguration"}}, "updateSchemaConfiguration": {"name": "info", "in": "body", "description": "Data for editing schema configuration", "required": true, "schema": {"$ref": "#/definitions/UpdateSchemaConfiguration"}}, "updateStakeRange": {"name": "stakeRange", "in": "body", "description": "Data for editing stake range", "required": true, "schema": {"$ref": "#/definitions/StakeRange"}}, "limitTemplateId": {"name": "limitTemplateId", "in": "path", "description": "Id of limit template", "required": true, "type": "string"}, "createLimitTemplate": {"name": "info", "in": "body", "description": "Data for creating limit template", "required": true, "schema": {"$ref": "#/definitions/CreateLimitTemplate"}}, "updateLimitTemplate": {"name": "info", "in": "body", "description": "Data for editing limit template", "required": true, "schema": {"$ref": "#/definitions/UpdateLimitTemplate"}}, "setDomainTags": {"name": "info", "in": "body", "description": "Data for editing domain tags", "required": true, "schema": {"$ref": "#/definitions/SetDomainTags"}}, "tableId__in": {"name": "tableId__in", "in": "query", "type": "string", "description": "table ids to get live info for separated with string. omit it to return all the tables", "required": false}, "userType": {"name": "userType", "in": "query", "description": "User type", "required": false, "type": "string", "enum": ["bo", "operator_api", "studio_user", "no_type"]}, "roleId__in": {"name": "roleId__in", "in": "query", "type": "string", "description": "list of public role ids separated by commas", "required": false}, "roleId__in!": {"name": "roleId__in!", "in": "query", "type": "string", "description": "list of public role ids to exclude separated by commas", "required": false}, "customDataRoleId__in": {"name": "customDataRoleId__in", "in": "query", "type": "string", "description": "list of custom data role ids separated by commas", "required": false}, "customDataRoleId__in!": {"name": "customDataRoleId__in!", "in": "query", "type": "string", "description": "list of custom data role ids to exclude separated by commas", "required": false}, "setGameClientFeatures": {"name": "clientFeatures", "in": "body", "description": "Client features for game", "required": true, "schema": {"$ref": "#/definitions/ClientFeatures"}}, "CheckWebSiteWhitelisted": {"name": "CheckWebSiteWhitelisted", "in": "body", "description": "Website whitelisted check", "required": true, "schema": {"$ref": "#/definitions/CheckWebSiteWhitelisted"}}, "deploymentGroupId": {"name": "deploymentGroupId", "in": "path", "description": "Deployment group id", "required": true, "type": "string"}, "overrideDefault": {"in": "query", "name": "overrideDef<PERSON>", "description": "Indicates if need to override default limits by game group limits", "required": false, "type": "boolean"}, "deploymentGroupRoute": {"name": "deploymentGroupRoute", "in": "path", "description": "Deployment group route", "required": true, "type": "string"}, "directTransferData": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DirectTransferDataInfo"}}, "gameCodeOnly": {"in": "query", "name": "gameCodeOnly", "description": "Return only game codes", "required": false, "type": "boolean"}, "domain": {"name": "domain", "in": "path", "description": "Entity domain", "required": true, "type": "string"}, "auditsSummaryId": {"name": "auditsSummaryId", "in": "query", "type": "string", "description": "Audit Summary Id", "required": false}, "auditsSummaryId__in": {"name": "auditsSummaryId__in", "in": "query", "type": "string", "description": "Summary of an audit summary id separated by commas", "required": false}, "auditsSummaryId__in!": {"name": "auditsSummaryId__in!", "in": "query", "type": "string", "description": "Summary of an audit summary id to exclude separated by commas", "required": false}, "auditsSummaryEventName": {"name": "auditsSummaryEventName", "in": "query", "type": "string", "description": "Event name of an audit summary", "required": false}, "auditsSummaryMethod": {"name": "auditsSummaryMethod", "in": "query", "type": "string", "description": "Method of an audit summary", "required": false, "enum": ["post", "put", "get", "patch", "delete", "cron", "service"]}, "auditsSummaryPath": {"name": "auditsSummaryPath", "in": "query", "type": "string", "description": "Path of an audit summary", "required": false}, "auditsSummaryPath__in": {"name": "auditsSummaryPath__in", "in": "query", "type": "string", "description": "Paths of audit summary separated by commas", "required": false}, "auditsSummaryPath__in!": {"name": "auditsSummaryPath__in!", "in": "query", "type": "string", "description": "Paths of audit summary to exclude separated by commas", "required": false}, "auditsSummarySummary": {"name": "auditsSummarySummary", "in": "query", "type": "string", "description": "Summary of an audit summary", "required": false}, "auditsSummaryMethod__in": {"name": "auditsSummaryMethod__in", "in": "query", "type": "string", "description": "Http method of an audit summary separated by commas", "required": false}, "auditsSummaryMethod__in!": {"name": "auditsSummaryMethod__in!", "in": "query", "type": "string", "description": "Http method of an audit summary to exclude separated by commas", "required": false}, "auditsSummarySummary__in": {"name": "auditsSummarySummary__in", "in": "query", "type": "string", "description": "Summary of an audit summary separated by commas", "required": false}, "auditsSummarySummary__in!": {"name": "auditsSummarySummary__in!", "in": "query", "type": "string", "description": "Summary of an audit summary to exclude separated by commas", "required": false}, "sessionId": {"name": "id", "in": "query", "type": "string", "description": "Session Id", "required": false}, "pathSessionId": {"name": "sessionId", "in": "path", "type": "string", "description": "Session Id", "required": true}, "pathUserId": {"name": "userId", "in": "path", "type": "number", "description": "User Id", "required": true}, "auditsSessionId": {"name": "auditsSessionId", "in": "query", "type": "string", "description": "Session Id", "required": false}, "startedAt": {"in": "query", "name": "startedAt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startedAt__gt": {"in": "query", "name": "startedAt__gt", "description": "start date greater than date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startedAt__lt": {"in": "query", "name": "startedAt__lt", "description": "start date less than date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "finishedAt": {"in": "query", "name": "finishedAt", "description": "finish date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "finishedAt__gt": {"in": "query", "name": "finishedAt__gt", "description": "finish date greater than date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "finishedAt__lt": {"in": "query", "name": "finishedAt__lt", "description": "finish date less than date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "eventName": {"name": "eventName", "in": "query", "type": "string", "description": "Event name", "required": false}, "tableId": {"name": "tableId", "in": "path", "type": "string", "description": "Physical Table Id", "required": true}, "integrationTestId": {"name": "id", "in": "path", "type": "integer", "description": "Test report id", "required": true}, "integrationTestReportFormat": {"name": "format", "in": "query", "type": "string", "enum": ["json", "html"], "required": false}, "integrationTestSortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string", "enum": ["id", "game_code", "merch_type", "merch_code", "status"]}, "merchantCode": {"name": "merchantCode", "in": "path", "type": "string", "required": true, "description": "merch code"}, "merchantCode__in": {"name": "code", "in": "query", "type": "string", "required": false, "description": "Part of merchant code"}, "sortHistoryBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string", "enum": ["id", "gameCode", "status", "createdAt"]}, "physicalTableIdStrictEquality": {"in": "query", "name": "physicalTableId", "description": "physicalTableId equal to value", "required": false, "type": "string"}, "physicalTableIdIn": {"in": "query", "name": "physicalTableId__in", "description": "list of physicalTableIds separated by commas", "required": false, "type": "string"}, "userIdStrictEquality": {"in": "query", "name": "userId", "description": "userId equal to value", "required": false, "type": "string"}, "userIdIn": {"in": "query", "name": "userId__in", "description": "list of userId separated by commas", "required": false, "type": "string"}, "providerGameCodeId": {"name": "providerGameCodeId", "in": "path", "type": "string", "description": "Provider Game Code Id", "required": true}, "historyMode": {"name": "mode", "in": "query", "description": "History url mode (for BO)", "required": false, "type": "string"}, "playerNickname": {"name": "playerNickname", "in": "path", "type": "string", "description": "player nickname", "required": true}, "playerNicknameContains": {"name": "nickname__contains", "in": "query", "type": "string", "description": "player nickname contains", "required": false}, "brandId": {"name": "brandId", "in": "query", "type": "number", "description": "brand id equal", "required": false}, "all": {"name": "all", "in": "query", "description": "Get objects true - all, false & undefined - own", "required": false, "type": "boolean"}, "levelId": {"name": "levelId", "in": "path", "description": "Level public id", "required": true, "type": "string"}, "gameTypeStrictEquality": {"name": "gameType", "in": "query", "type": "string", "description": "Game type", "enum": ["slot", "table", "action", "external"]}, "rtpDeduction__gte": {"in": "query", "name": "rtpDeduction__gte", "description": "rtpDeduction is not null and greater or equal required value", "required": false, "type": "number"}, "inherited": {"name": "inherited", "in": "query", "description": "If true, returns the domain pool from the entity's hierarchy (parent entities) if the entity itself doesn't have a domain pool assigned", "required": false, "type": "boolean", "default": false}, "waitForCompletion": {"in": "query", "name": "waitForCompletion", "description": "set this param false to not wait for async completion of underlying operation (for example, if its long-taking operation and you will check its result later), default value: true", "type": "boolean", "required": false}, "closeInSWWalletOnly": {"in": "query", "name": "closeInSWWalletOnly", "description": "Set this param to true if no finalization operation should reach the operator (the same as manualPayments)", "type": "boolean", "required": false}, "allowToFinishCurrentSession": {"in": "query", "name": "allowToFinishCurrentSession", "description": "Allow players to finish active sessions in case of suspending game. Default false.", "type": "boolean", "required": false}, "updateGameLimitsCurrency": {"name": "gameLimitsCurrency", "in": "body", "description": "Data for game limits currency", "required": true, "schema": {"type": "object", "properties": {"toEURMultiplier": {"type": "number", "description": "Currency multiplier to EUR", "example": 500}, "copyLimitsFrom": {"type": "string", "description": "Currency from which limits can be copied", "example": "CLP"}}}}, "createGameLimitsCurrency": {"name": "gameLimitsCurrency", "in": "body", "description": "Data for creating game limits currency", "required": true, "schema": {"$ref": "#/definitions/GameLimitsCurrency"}}, "whitelistType": {"name": "type", "in": "path", "description": "IP whitelist type [user, bo]", "required": true, "type": "string"}, "includeSubBrands": {"name": "includeSubBrands", "in": "query", "description": "If true, all current entity children's data will be included in the response", "required": false, "type": "boolean"}, "getGameContextsRequest": {"name": "getGameContextsRequest", "in": "body", "description": "Data for search game contexts", "required": true, "schema": {"type": "object", "properties": {"brandId": {"type": "string", "description": "encoded or decoded brand id", "example": "kx3W9eR1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_al"}, "playerCode": {"type": "string", "description": "Player code", "example": "vers<PERSON><PERSON>"}}}}, "issueCode": {"name": "issueCode", "description": "Add Jira issue id to audits log", "type": "string", "in": "query"}, "includeMerchantCode": {"name": "includeMerchantCode", "description": "Add to response info about merchant codes", "type": "boolean", "in": "query"}, "excludeInactiveGames": {"name": "excludeInactiveGames", "description": "Exclude suspended and hidden games from response", "type": "boolean", "in": "query"}, "jackpotInstanceInformation": {"name": "info", "in": "body", "description": "Jackpot instance", "required": true, "schema": {"$ref": "#/definitions/JackpotInstance"}}, "jackpotInstanceId": {"name": "id", "in": "path", "description": "Jackpot instance id", "required": true, "type": "string"}, "ChangeNickname": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChangeNicknameData"}}}
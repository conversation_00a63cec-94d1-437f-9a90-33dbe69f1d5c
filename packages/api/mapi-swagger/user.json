{"/entities/{path}/users": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:create"]}], "tags": ["User"], "summary": "Creates new user under a specific parent by path", "parameters": [{"$ref": "#/parameters/createUser"}], "responses": {"201": {"description": "Created user", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 625: Add Role to User: failed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}, "409": {"description": "- 200: User already exist\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Finds users under the key entity tree by path", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/userIdIn"}, {"$ref": "#/parameters/username__equal"}, {"$ref": "#/parameters/username__contains"}, {"$ref": "#/parameters/username__contains!"}, {"$ref": "#/parameters/username__in"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/entity__equal"}, {"$ref": "#/parameters/entity__contains"}, {"$ref": "#/parameters/entity__contains!"}, {"$ref": "#/parameters/entity__in"}, {"$ref": "#/parameters/fullTextSearchQuery"}, {"$ref": "#/parameters/fullTextSearchFields"}, {"$ref": "#/parameters/userType"}, {"$ref": "#/parameters/roleId__in"}, {"$ref": "#/parameters/roleId__in!"}, {"$ref": "#/parameters/customData"}, {"$ref": "#/parameters/customDataRoleId__in"}, {"$ref": "#/parameters/customDataRoleId__in!"}], "responses": {"200": {"description": "List of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/DetailedUserInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users/create-options": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:create"]}], "tags": ["User"], "summary": "Returns user create options by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Create options", "schema": {"$ref": "#/definitions/UserCreateOptions"}}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side"}}}}, "/users": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:create"]}], "tags": ["User"], "summary": "Creates new user for key entity", "parameters": [{"$ref": "#/parameters/createUser"}], "responses": {"201": {"description": "Created user", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 625: Add Role to User: failed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}, "409": {"description": "- 200: User already exist\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:view"]}], "tags": ["User"], "summary": "Finds users under the key entity tree", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/userIdIn"}, {"$ref": "#/parameters/username__equal"}, {"$ref": "#/parameters/username__contains"}, {"$ref": "#/parameters/username__contains!"}, {"$ref": "#/parameters/username__in"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/entity__equal"}, {"$ref": "#/parameters/entity__contains"}, {"$ref": "#/parameters/entity__contains!"}, {"$ref": "#/parameters/entity__in"}, {"$ref": "#/parameters/fullTextSearchQuery"}, {"$ref": "#/parameters/fullTextSearchFields"}, {"$ref": "#/parameters/userType"}, {"$ref": "#/parameters/roleId__in"}, {"$ref": "#/parameters/roleId__in!"}, {"$ref": "#/parameters/customData"}, {"$ref": "#/parameters/customDataRoleId__in"}, {"$ref": "#/parameters/customDataRoleId__in!"}], "responses": {"200": {"description": "List of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/DetailedUserInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/users/create-options": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:create"]}], "tags": ["User"], "summary": "Returns user create options", "parameters": [], "responses": {"200": {"description": "Create options", "schema": {"$ref": "#/definitions/UserCreateOptions"}}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side"}}}}, "/entities/{path}/users/{username}/email/force-set": {"post": {"security": [{"apiKey": []}, {"Permissions": ["user-extra:email:force-set"]}], "tags": ["User"], "summary": "Directly changes user email under a specific parent by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ChangeEmailData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 199: Email already used\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/email/force-set": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:email:force-set"]}], "tags": ["User"], "summary": "Changes user email", "parameters": [{"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ChangeEmailData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 199: Email already used\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 198: User does not exist\n"}}}}, "/entities/{path}/users/{username}/password": {"post": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:change-password"]}], "tags": ["User"], "summary": "Changes user password under a specific parent by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ChangePasswordData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 203: Password incorrect\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/password": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:change-password"]}], "tags": ["User"], "summary": "Changes key entity's user password", "parameters": [{"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ChangePasswordData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 203: Password incorrect\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/entities/{path}/users/{username}/password/force-reset": {"post": {"security": [{"apiKey": []}, {"Permissions": ["user-extra:force-reset-password"]}], "tags": ["User"], "summary": "Reset user password without old password under a specific parent by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ForceResetPasswordData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error: One of your previous password was the same\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 228: New password should be different from a previous one", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 203: Password incorrect\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/password/force-reset": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:force-reset-password"]}], "tags": ["User"], "summary": "Changes key entity's user password", "parameters": [{"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ForceResetPasswordData"}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error: One of your previous password was the same\n- 43: Password is not valid\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 228: New password should be different from a previous one", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 203: Password incorrect\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/entities/{path}/users/{username}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:change-state"]}], "tags": ["User"], "summary": "Suspends user by path", "description": "Changes the status of the user to suspended", "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:change-state"]}], "tags": ["User"], "summary": "Restores user by path", "description": "Changes the status of the user to normal", "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/suspended": {"parameters": [{"$ref": "#/parameters/username"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:change-state"]}], "tags": ["User"], "summary": "Suspends key entity's user", "description": "Changes the status of the key entity's user to suspended", "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:change-state"]}], "tags": ["User"], "summary": "Restores key entity's user", "description": "Changes the status of the key entity's user to normal", "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/entities/{path}/users/{username}/login-lock": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["user-extra:login-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user login by path", "responses": {"204": {"description": "User was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 232 User isn't locked\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/{username}/login-lock": {"parameters": [{"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:login-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user login", "responses": {"204": {"description": "User was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 232 User isn't locked\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users/{username}/change-password-lock": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["user-extra:change-password-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user changing password by path", "responses": {"204": {"description": "Changing password for user was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 233 Changing password is available for user\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/{username}/change-password-lock": {"parameters": [{"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:change-password-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user login", "responses": {"204": {"description": "Changing password for user was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 233 Changing password is available for user\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/{ip}/reset-password-lock": {"parameters": [{"name": "ip", "in": "path", "description": "IP address", "required": true, "type": "string"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:change-password-unlock"]}], "tags": ["User"], "summary": "Unlock password reset attempts by IP address", "description": "Clears password reset blocking for a specific IP address", "responses": {"204": {"description": "Password reset attempts were unlocked for the IP address"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 233 Changing password is available for user\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users/group/status": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:edit", "user:change-state"]}], "tags": ["User"], "summary": "Sets status for group of users under a specific brand by path", "parameters": [{"$ref": "#/parameters/userGroupStatus"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/group/status": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:edit", "keyentity:user:change-state"]}], "tags": ["User"], "summary": "Sets status for group of users under key entity", "parameters": [{"$ref": "#/parameters/userGroupStatus"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/login": {"post": {"tags": ["User"], "summary": "Logs user in", "description": "Logs user in by entity's secret key and credentials", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["secret<PERSON>ey", "username", "password"], "properties": {"secretKey": {"type": "string", "description": "secret key", "example": "key"}, "username": {"type": "string", "description": "username", "example": "USER1"}, "password": {"type": "string", "format": "password", "description": "password", "example": "123456qaB"}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 998: <PERSON>gin Failed\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 753: Two factor auth code has been sent recently. Repeat attempt a little later\n"}, "500": {"description": "- 719: An error occurred when sending sms\n- 720: An error occurred when sending email\n"}}}}, "/logout": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "parameters": [{"name": "deleteSessions", "in": "query", "description": "Delete all user sessions", "required": false, "type": "boolean"}], "summary": "Logs user out", "description": "Logs user out by ending his session.", "responses": {"204": {"description": "Log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired\n"}}}}, "/users/{userId}/session/{sessionId}": {"parameters": [{"$ref": "#/parameters/pathSessionId"}, {"$ref": "#/parameters/pathUserId"}], "delete": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Kills user session", "description": "Forcefully logs user out by ending his session.", "responses": {"204": {"description": "Forced log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired\n"}}}}, "/login/secondstep/challenge": {"post": {"tags": ["User"], "summary": "Challenges user with selected auth type on login or first auth type selection", "description": "Sends user a challenge using selected auth type. Should be used to challenge user on login (when he picks auth type that differs from the default auth type) or when user selects auth type for the first time", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["authType", "token"], "properties": {"authType": {"type": "string", "description": "Selected type for second step authentication", "example": "sms"}, "token": {"type": "string", "description": "2FA token for verifying user's request. Must be returned from /login endpoint", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "contactInfo": {"type": "string", "description": "Phone number for sms auth type. Param must be specified for sms type only.", "example": "+375291234567"}}}}], "responses": {"200": {"description": "Returned in case auth code was sent to user\n", "schema": {"$ref": "#/definitions/ChallengeSentInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 699: Provided auth code is incorrect\n- 721: Provided auth type is not allowed\n- 722: Two factor auth is not configured\n- 723: Two factor auth code was not generated or expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n- 704: Two factor auth token is expired\n- 718: Two factor auth token error\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}, "500": {"description": "- 719: An error occurred when sending sms\n- 720: An error occurred when sending email\n"}}}}, "/login/secondstep/confirm": {"post": {"tags": ["User"], "summary": "Verify received auth code to confirm selected auth type on first login auth type selection", "description": "Verifies and sets selected auth type on first login", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["authType", "token", "authCode"], "properties": {"authType": {"type": "string", "description": "Selected type for second step authentication", "example": "sms"}, "token": {"type": "string", "description": "2FA token for verifying user's request. Must be returned from /login endpoint", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "authCode": {"type": "string", "description": "Auth code that was received by user", "example": "645123"}, "contactInfo": {"type": "string", "description": "Phone number for sms auth type. May be omitted, if email was selected - one from user profile will be used", "example": "+375291234567"}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 699: Provided auth code is incorrect\n- 721: Provided auth type is not allowed\n- 722: Two factor auth is not configured\n- 723: Two factor auth code was not generated or expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n- 704: Two factor auth token is expired\n- 718: Two factor auth token error\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/login/secondstep": {"post": {"tags": ["User"], "summary": "Logs user in using second step auth data", "description": "Verifies auth code and logs in user", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["token", "authCode"], "properties": {"token": {"type": "string", "description": "2FA token for verifying user's request. Must be returned from /login endpoint", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "authCode": {"type": "string", "description": "Auth code that was received by user", "example": "645123"}, "authType": {"type": "string", "description": "Auth type selected by user", "example": "email", "enum": ["sms", "email", "google"]}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 699: Provided auth code is incorrect\n- 721: Provided auth type is not allowed\n- 722: Two factor auth is not configured\n- 723: Two factor auth code was not generated or expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n- 704: Two factor auth token is expired\n- 718: Two factor auth token error\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/login/secondstep/reset/{username}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:edit", "keyentity:user:create"]}], "tags": ["User"], "summary": "Removes user's second step auth type", "description": "Removes user's second step auth type", "parameters": [{"$ref": "#/parameters/username"}, {"in": "body", "name": "info", "required": true, "schema": {"required": ["authType"], "properties": {"authType": {"type": "string", "description": "Auth type to remove for user", "example": "email", "enum": ["sms", "email", "google"]}}}}], "responses": {"204": {"description": "Auth type was removed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/entities/{path}/login/secondstep/reset/{username}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:edit", "user:create"]}], "tags": ["User"], "summary": "Removes user's second step auth type by path", "description": "Removes user's second step auth type", "parameters": [{"$ref": "#/parameters/username"}, {"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"required": ["authType"], "properties": {"authType": {"type": "string", "description": "Auth type to remove for user", "example": "email", "enum": ["sms", "email", "google"]}}}}], "responses": {"204": {"description": "Auth type was reset"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/login/secondstep/setdefault": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Set selected auth type as default one", "description": "Set selected auth type as default one", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["authType"], "properties": {"authType": {"type": "string", "description": "Auth type to set as default for user", "example": "email", "enum": ["sms", "email", "google"]}}}}], "responses": {"204": {"description": "Auth type was added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/login/secondstep/challenge-on-add": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Challenges user when adding second step auth type", "description": "Challenges user when adding second step auth type", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["authType"], "properties": {"authType": {"type": "string", "description": "Auth type to add for user", "example": "email", "enum": ["sms", "email", "google"]}}}}], "responses": {"200": {"description": "Challenge was sent", "schema": {"$ref": "#/definitions/ChallengeSentInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/login/secondstep/confirm-add": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Confirm adding second step auth type", "description": "Confirm adding second step auth type", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["authType"], "properties": {"authType": {"type": "string", "description": "Auth type to add for user", "example": "email", "enum": ["sms", "email", "google"]}, "authCode": {"type": "string", "description": "Auth code that was received by user", "example": "645123"}, "setAsDefault": {"type": "boolean", "description": "True to set this auth type as the default one", "example": true}}}}], "responses": {"204": {"description": "Auth type was added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/login/secondstep/authtypes": {"get": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Gets user authtypes info", "description": "Gets user authtypes info", "parameters": [], "responses": {"200": {"description": "User auth types", "schema": {"$ref": "#/definitions/UserAuthTypesInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 715: Two Factor Authentication is not set for user\n"}}}}, "/login/refresh": {"post": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["User"], "summary": "Refreshes access token before expiration", "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/login/refresh-captcha": {"post": {"tags": ["User"], "summary": "Requests to refresh a captcha", "responses": {"200": {"description": "New captcha data", "schema": {"$ref": "#/definitions/CaptchaInfo"}}}}}, "/login/password/confirm": {"post": {"tags": ["User"], "summary": "Confirms the password change request", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["secret<PERSON>ey", "username", "token", "newPassword"], "properties": {"secretKey": {"type": "string", "description": "secret key", "example": "key"}, "username": {"type": "string", "description": "username", "example": "USER1"}, "token": {"type": "string", "format": "byte", "description": "password reset token", "example": "jW/0wgBWz4k7WfcpBOJz7dn4X/M0wobslXvbmkFX+4wOALVjElCiu5quL9zPA833n4WW2plevA5MC1RwU9d26Q"}, "newPassword": {"type": "string", "format": "password", "example": "123456newB"}}}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/login/password/reset": {"post": {"tags": ["User"], "summary": "Requests to reset password", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["secret<PERSON>ey", "identifier"], "properties": {"secretKey": {"type": "string", "example": "key"}, "identifier": {"type": "string", "description": "username or email", "example": "<EMAIL>"}, "csrfToken": {"type": "string", "description": "csrf token", "example": "rVkdd8zBZxMo2Tno86ZCv/yV38hfTDI5+jigqVHG/lNXvYu+wmt7vPYjDSV7EJovg5JNMfEXLsb1Ls9GUqdxUzZVCpPZKjAup6S8NTT6rU1k83FUj0BQLefTyvkMdvVFCmCmfUstOfY6T/mGj6pYzl8ebd8GFI8IKIQbzgV2TDLWzWQgHXKSI55NI+5f8w=="}, "captchaToken": {"type": "string", "example": "12345"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"extraData": {"$ref": "#/definitions/CaptchaInfo"}}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 235: reset password is blocked\n"}}}}, "/entities/{path}/users/{username}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Gets user details by path", "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/DetailedUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:edit"]}], "tags": ["User"], "summary": "Changes user details by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PatchUserData"}}], "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["user-extra:delete"]}], "tags": ["User"], "summary": "Deletes user by path", "responses": {"204": {"description": "User was deleted"}, "400": {"description": "Returned in case we have error on the server side - 62: One of the parents is suspended", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error - 205: Access Token is expired"}, "403": {"description": "- 206: Forbidden"}, "404": {"description": "- 51: Could not find entity - 198: User does not exist"}}}}, "/entities/{path}/users/{username}/type": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:change-type"]}], "tags": ["User"], "summary": "Changes user type", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UserTypeInfo"}}], "responses": {"200": {"description": "User type successfully changed", "schema": {"$ref": "#/definitions/UserTypeInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/type": {"parameters": [{"$ref": "#/parameters/username"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:change-type"]}], "tags": ["User"], "summary": "Changes user type for keyentity", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UserTypeInfo"}}], "responses": {"200": {"description": "User type successfully changed", "schema": {"$ref": "#/definitions/UserTypeInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}": {"parameters": [{"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:view"]}], "tags": ["User"], "summary": "Gets key user details", "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/DetailedUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:edit"]}], "tags": ["User"], "summary": "Changes key user details", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PatchUserData"}}], "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user-extra:delete"]}], "tags": ["User"], "summary": "Deletes user", "responses": {"204": {"description": "User was deleted"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/profile": {"parameters": [{"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:view"]}], "tags": ["User"], "summary": "Gets key user details", "responses": {"200": {"description": "Users detailed info with information about blocking", "schema": {"$ref": "#/definitions/DetailedUserInfoWithBlockingInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/entities/{path}/users/{username}/profile": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Gets key user details by path", "responses": {"200": {"description": "Users detailed info with information about blocking", "schema": {"$ref": "#/definitions/DetailedUserInfoWithBlockingInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/email": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Requests to change user email / Changes user email", "parameters": [{"in": "body", "name": "info", "required": false, "description": "if body empty sends request to change email otherwise sets new email", "schema": {"type": "object", "properties": {"token": {"type": "string", "format": "byte", "example": "4dEnMxCNbOwEYi9OsletrQEFcUIMkgHL/mZw1z1cY2iuXyP4tG6+i0+hZ3lwP6n1fz1GO/6OVaaD8322PSkFgA"}, "newEmail": {"type": "string", "example": "<EMAIL>"}}}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/users/email/confirmation": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Confirms change of the current user email", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"type": "object", "properties": {"token": {"type": "string", "format": "byte", "example": "4dEnMxCNbOwEYi9OsletrQEFcUIMkgHL/mZw1z1cY2iuXyP4tG6+i0+hZ3lwP6n1fz1GO/6OVaaD8322PSkFgA"}}}}], "responses": {"200": {"description": "User information", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}}
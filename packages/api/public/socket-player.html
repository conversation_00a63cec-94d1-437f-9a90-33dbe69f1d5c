<!doctype html>
<html lang="en">
<head>
    <title>Player Sockets</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font: 13px Helvetica, Arial;
        }

        form {
            padding: 3px;
            bottom: 0;
            width: 100%;
        }

        #messages {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        #messages li {
            padding: 5px 10px;
        }

        #messages li:nth-child(odd) {
            background: #eee;
        }
    </style>
</head>
<body>

<form id="get-info">
    <input id="player_token" autocomplete="off" placeholder="Player Token"/>
    <button type="submit">Get Player Info</button>
</form>

<ul id="messages"></ul>

<script src="/socket.io/socket.io.js"></script>
<script src="https://code.jquery.com/jquery-1.11.1.js"></script>
<script>
    let socket;

    $('#get-info').submit(function () {
        if (!socket) {
            socket = io('/', {
                path: '/socket-v4',
                query: {
                    sw_player_token: $('#player_token').val()
                }
            });
            socket.on('player-error', function (response) {
                $('#messages').append($('<li>').text('ERROR: ' + JSON.stringify(response)));
            });
               
            socket.on('player-info', function (response) {
                $('#messages').append($('<li>').text('DATA: ' + JSON.stringify(response)));
            });
        }
                    
        socket.emit('get-player-info');
        return false;
    });
</script>
</body>
</html>

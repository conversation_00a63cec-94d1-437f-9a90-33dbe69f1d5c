-- Inputs:
-- KEYS[1] = Redis key for player bonus hash
-- ARGV[1] = currency to match
-- ARGV[2] = gameCode to match

local hashKey = KEYS[1]
local currency = ARGV[1]
local gameCode = ARGV[2]

-- Function to check if gameCode matches (handles both string and array)
local function isGameCodeMatch(bonusGameCode, targetGameCode)
    if not bonusGameCode then
        return false
    end

    -- If it's a string, do direct comparison
    if type(bonusGameCode) == "string" then
        return bonusGameCode == targetGameCode
    end

    -- If it's a table (JSON array), iterate through it
    if type(bonusGameCode) == "table" then
        for _, game in ipairs(bonusGameCode) do
            if game == targetGameCode then
                return true
            end
        end
    end

    return false
end

-- Get all player bonuses from the hash
local bonuses = redis.call("HGETALL", hashKey)

-- Collect filtered, parsed bonuses
local matchedBonuses = {}

for i = 1, #bonuses, 2 do
    local bonusId = bonuses[i]
    local bonusJson = bonuses[i + 1]
    local ok, bonus = pcall(cjson.decode, bonusJson)

    if ok and bonus.currency == currency and isGameCodeMatch(bonus.gameCode, gameCode) then
        bonus.id = bonusId
        table.insert(matchedBonuses, bonus)
    end
end

-- Sort by createdAt
table.sort(matchedBonuses, function(a, b)
    return a.createdAt < b.createdAt
end)

-- Delete the first matched bonus (earliest one)
if #matchedBonuses > 0 then
    local toDelete = matchedBonuses[1]
    redis.call("HDEL", hashKey, toDelete.id)
    return cjson.encode(toDelete)
else
    return nil
end

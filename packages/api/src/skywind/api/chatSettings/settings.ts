import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { parseStartGameToken } from "../../utils/token";
import logger from "../../utils/logger";
import { ChatService } from "../../services/chatService";
const log = logger("chat-settings");

export default function (router: FastifyInstance, options, done) {
    router.post("/chat-settings", getSettings);
    done();
}

export async function getSettings(req: FastifyRequest<{ Body: { startGameToken: string | object }}> & Request, res: Response) {
    const body = req.body;
    let chatSettings = ChatService.getDefaultSettings();
    try {
        const decoded: StartGameTokenData = await parseStartGameToken(body.startGameToken);
        chatSettings = await ChatService.getSettings(decoded);
    } catch (error) {
        log.error(error);
    }
    return res.send({ ...chatSettings });
}

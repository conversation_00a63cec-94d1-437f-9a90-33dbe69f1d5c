import * as express from "express";
import { v4 } from "uuid";
import { BaseEntity, ChildEntity, ENTITY_TYPE, EntityInfo } from "../entities/entity";
import {
    auditable,
    authenticate,
    authorize,
    currencyValidator,
    decodePid,
    defineLimits,
    defineLimitsWithBalance,
    FORMAT_CSV,
    FormattedResponse,
    getBooleanParamFromRequestQuery,
    getBrand,
    getEntity,
    getEntityPath,
    isMasterEntity,
    languageValidator,
    sanitizeBoolean,
    sanitizeCommaSeparatedString,
    validate,
    validateChangePasswordValidator
} from "./middleware/middleware";
import { BrandEntity } from "../entities/brand";
import {
    CreateData as CreatePlayerData,
    default as getPlayerService,
    getBrandPlayerService,
    queryParamsKeys
} from "../services/brandPlayer";
import { getBrandPlayerValidator } from "../services/brandPlayerValidator";
import { hasPermissions, KeyEntityHolder, PermissionsHolder, UserInfoHolder } from "../services/security";
import * as EntityService from "../services/entity";
import EntityCache from "../cache/entity";
import EntityFinance from "../services/entityFinance";
import * as UserService from "../services/user/user";
import getUserService from "../services/user/user";
import * as Errors from "../errors";
import { Player, PlayerInfo } from "../entities/player";
import { PaymentMethodCreateData, PaymentMethodInfo } from "../entities/payment_method";
import * as PaymentMethodService from "../services/payment";
import { getEntityPlayerFinanceService } from "../services/payment";
import { parseFilter, prepareScheme } from "../services/filter";
import {
    CHANGE_USER_TYPE_VALIDATOR,
    CHANGE_USER_VALIDATOR,
    changeUserType,
    CREATE_USER_VALIDATOR,
    createOrUpdatePlayersInfo,
    deleteUserHandler,
    findByDomain,
    findByKeys,
    getPlayersInfo,
    getShortStructure,
    getStructure,
    getUser,
    getUserCreateOptions,
    getUserDetails,
    getUserProfileWithBlocking,
    getUserValidator,
    patchPlayer,
    paymentDepositsHandler,
    paymentGeneratePublicKeyHandler,
    paymentGetPublicKeyHandler,
    paymentSetPublicKeyHandler,
    paymentWithdrawalsHandler,
    resetPasswordHandler,
    resetPasswordValidator,
    unlockPlayer,
    unlockChangingPasswordUser,
    unlockUser,
    validateCreatePlayer,
    validatePlayerFinance,
    validateRegisterPlayer,
    validateShortStructure,
    validateUsername
} from "./keyentity";
import { AuditInfo } from "../utils/auditHelper";
import { CreateData, default as getEntityFactory } from "../services/entityFactory";
import { validatePlayerCodeForOperator, validatePlayerInfo, validatePlayerUpdate } from "./playerAuthForBrands";
import { normalizeCurrencyAmount } from "./payment";
import { OrderInfo, TransferData } from "../entities/payment";
import { EntityBulkOperationResult } from "../entities/bulk";
import { getUserPasswordService } from "../services/user/userPassword";
import { EntityBulkService } from "../services/bulk/entityBulkService";
import { getBulkOperationResultInfo } from "../utils/auditExtractors";
import { UserInfo } from "../entities/user";
import { CreateData as CreateUserData, UpdateData as UpdateUserData } from "../../skywind/services/user/user";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

export const validateEntityCreation = {
    name: { notEmpty: true, isWord: true },
    deploymentGroupRoute: { optional: true, isWord: true },
    defaultCurrency: currencyValidator,
    defaultLanguage: languageValidator
};

export const validateEntityUpdate = {
    deploymentGroupRoute: { optional: true, isWord: true },
    defaultCurrency: { ...currencyValidator, optional: true },
    defaultLanguage: { ...languageValidator, optional: true },
    status: { optional: true, isStatus: true }
};

router.post("/bulk-operation", authenticate, authorize,
    decodePid({ keysToParse: ["item"] }), auditable, bulkOperationHandler);

async function bulkOperationHandler(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = new EntityBulkService();
        const results: EntityBulkOperationResult[] = await service.process(entity, req.body);

        res.status(201).send(getBulkOperationResultInfo(results));
    } catch (err) {
        next(err);
    }
}

router.get("/:path/users/:username/permissions",
    authenticate,
    authorize,
    validateUsername,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const username: string = req.params["username"];
        const entity: BaseEntity = req.keyEntity.find({ path: getEntityPath(req) });
        if (!entity) {
            return next(new Errors.EntityCouldNotBeFound());
        }
        try {
            const service = getUserService(entity);
            res.send(await service.getPermissionList(username));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Find sub entity under the main key and return the structure of this entity
 *
 */
router.get("/:path/structure",
    authenticate,
    authorize,
    sanitizeBoolean("includeProxy", "includeMerchantCode"),
    getStructure
);

/**
 * Find sub entity under the main key and return the short structure of this entity
 *
 */
router.get("/:path/short-structure",
    authenticate,
    authorize,
    validateShortStructure,
    sanitizeCommaSeparatedString("additionalFields"),
    getShortStructure
);

/**
 * Get specific entity information, no child entities and including all balances
 *
 */

router.put("/:path/players/info",
    authenticate,
    authorize,
    validatePlayerInfo,
    createOrUpdatePlayersInfo);

router.get("/:path/players/info",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    defineLimits,
    getPlayersInfo);

router.get("/:path",
    authenticate,
    authorize,
    getEntityDetails);

export async function getEntityDetails(req: Request & PermissionsHolder,
                                       res: express.Response,
                                       next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const info: EntityInfo = await entity.toInfoWithBalances(hasPermissions(req, ["id:decode"]));
        if (entity.isBrand()) {
            await getEntityJurisdictionService().includeTo(info);
        }
        res.send(info);
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Create a new entity
 * find the parent under the main key, and add the new entity as child entity
 *
 */
router.post("/",
    authenticate,
    authorize,
    validate(validateEntityCreation),
    auditable,
    createEntity);

export async function createEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const data: CreateData = req.body;
        if (!!data.type && data.type === ENTITY_TYPE.MERCHANT) {
            throw new Errors.OperationForbidden(
                "You can't create entity with type merchant with this operation"
            );
        }

        const entity = getEntity(req);
        const factory = getEntityFactory(entity);
        const createdEntity: BaseEntity = await factory.createEntity(data).finally(() => EntityCache.reset());

        const entityInfo = await createdEntity.toInfoWithBalances();
        res.status(201).send(entityInfo);
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Remove an entity
 * find and entity under the main key, and removes it, if it's empty
 *
 */
router.delete("/:path",
    authenticate,
    authorize,
    auditable,
    deleteEntity);

export async function deleteEntity(req: Request, res: express.Response, next: express.NextFunction) {
    const keyEntity: BaseEntity = req.keyEntity;
    try {
        res.send(await EntityService.removeEntity(keyEntity, { path: getEntityPath(req) })
            .finally(() => EntityCache.reset()));
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Update entity information, no child entities and including all balances
 *
 */
router.patch("/:path",
    authenticate,
    authorize,
    validate(validateEntityUpdate),
    auditable,
    updateEntity);

export async function updateEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const factory = getEntityFactory(entity);

        delete req.body.status;

        const info: EntityInfo = await
            factory.update(req.body, { forbidToUpdateIsTest: (entity as BrandEntity).isMerchant })
                .finally(() => EntityCache.reset());
        if (!info) {
            return next(new Errors.EntityCouldNotBeFound());
        }

        res.send(info);
    } catch (err) {
        next(err);
    }
}

router.post("/:path",
    authenticate,
    authorize,
    validate(validateEntityCreation),
    auditable,
    createEntity);

/**
 * Change status of entity to suspended
 *
 */
router.put("/:path/suspended",
    authenticate,
    authorize,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.suspend(req.keyEntity, { path: getEntityPath(req) }));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change status of entity to normal
 *
 */
router.delete("/:path/suspended",
    authenticate,
    authorize,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.restore(req.keyEntity, { path: getEntityPath(req) }));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change status of entity to maintenance
 *
 */
router.put("/:path/maintenance",
    authenticate,
    authorize,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.turnMaintenanceOn(req.keyEntity, { path: getEntityPath(req) }));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change status of entity to normal
 *
 */
router.delete("/:path/maintenance",
    authenticate,
    authorize,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.turnMaintenanceOff(req.keyEntity, { path: getEntityPath(req) }));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change the status of entity to test
 *
 */
router.put("/:path/test",
    authenticate,
    authorize,
    sanitizeBoolean("force"),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.turnTestStatusOn(
                req.keyEntity, { path: getEntityPath(req) }, req.query.force)
            );
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change the status of entity from test to normal
 *
 */
router.delete("/:path/test",
    authenticate,
    authorize,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await EntityService.turnTestStatusOff(req.keyEntity, { path: getEntityPath(req) }));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Credit entity
 *
 */
router.post("/:path/credits/:currency/:amount",
    authenticate,
    authorize,
    validate({
        currency: currencyValidator,
        amount: { notEmpty: true, isDecimal: true, isPositive: true }
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = EntityService.getChildEntity(req.keyEntity, { path: getEntityPath(req) });
            const financeService = new EntityFinance(entity);
            const info: EntityInfo = await financeService.credit(
                req.params.currency, req.params.amount, AuditInfo.getUsername(req));
            if (!info) {
                return next(new Errors.EntityCouldNotBeFound());
            }

            res.send(info);
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Debit entity
 *
 */
router.post("/:path/debits/:currency/:amount",
    authenticate,
    authorize,
    validate({
        currency: currencyValidator,
        amount: { notEmpty: true, isDecimal: true, isPositive: true },
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = EntityService.getChildEntity(req.keyEntity, { path: getEntityPath(req) });
            const financeService = new EntityFinance(entity);
            const info: EntityInfo = await financeService.debit(
                req.params.currency, req.params.amount, AuditInfo.getUsername(req));
            if (!info) {
                return next(new Errors.EntityCouldNotBeFound());
            }

            res.send(info);
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Create new player
 */
router.post("/:path/players",
    authenticate,
    authorize,
    validateCreatePlayer,
    auditable,
    createPlayer);

router.post("/:path/players/register",
    authenticate,
    authorize,
    validateRegisterPlayer,
    auditable,
    createPlayer);

async function createPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    const data: CreatePlayerData = req.body;
    try {
        const entity = getBrand(req);
        const player = await getPlayerService().create(entity, data);
        res.status(201).send(await player.toInfo());
    } catch (err) {
        next(err);
    }
}

router.get("/:path/players",
    authenticate,
    authorize,
    defineLimits,
    defineLimitsWithBalance,
    validate(prepareScheme(["limit", "offset", "sortOrder", "status"])),
    async (req: Request, res: FormattedResponse, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            const isCSV = req.query.format === FORMAT_CSV;
            const withBalance = !isCSV || isCSV && req.query.withBalance === "true";
            const players = await getBrandPlayerService().search(
                entity,
                parseFilter(req.query, queryParamsKeys),
                withBalance,
                getBooleanParamFromRequestQuery(req, "withoutGameGroup")
            );
            res.sendFormatted(req, players, ["customData"]);
        } catch (err) {
            next(err);
        }
    });

router.get("/:path/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const playerCode: string = req.params.playerCode;
        try {
            const entity = getBrand(req);
            const fetchAuditData = getBooleanParamFromRequestQuery(req, "withAudit");
            const withLastAction = getBooleanParamFromRequestQuery(req, "withLastAction");
            const player: Player = await getBrandPlayerService().findOneExtended(entity,
                { code: playerCode },
                fetchAuditData,
                withLastAction);
            res.send(await player.toInfoWithBalances());
            next();
        } catch (err) {
            next(err);
        }
    });

router.patch("/:path/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerUpdate,
    auditable,
    patchPlayer
);

router.put("/:path/players/:playerCode/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getBrand(req);
            const player = await getBrandPlayerService().updateGameGroup(entity,
                { code: req.params["playerCode"] },
                req.params["gameGroup"]);
            res.send(player);
        } catch (err) {
            next(err);
        }
    });

router.post("/:path/players/:playerCode/deposits/:currency/:amount",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validatePlayerFinance,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const externalTrx: string = req.body.exTrxId;
        try {
            const entity: BrandEntity = getBrand(req);
            const data: TransferData = {
                playerCode: req.params["playerCode"],
                currency: req.params.currency,
                amount: normalizeCurrencyAmount(req.params.currency, req.params.amount),
                extTrxId: externalTrx !== undefined ? externalTrx : v4()
            };
            const transactionInfo: OrderInfo = await getEntityPlayerFinanceService().transferIn(entity, data);
            const responseStatus = transactionInfo.isNew ? 201 : 200;
            delete transactionInfo.isNew;
            res.status(responseStatus).send(transactionInfo);
        } catch (err) {
            next(err);
        }
    });

router.post("/:path/players/:playerCode/withdrawals/:currency/:amount",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validatePlayerFinance,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const externalTrx: string = req.body.exTrxId;
        try {
            const entity: BrandEntity = getBrand(req);
            const data: TransferData = {
                playerCode: req.params["playerCode"],
                currency: req.params.currency,
                amount: normalizeCurrencyAmount(req.params.currency, req.params.amount),
                extTrxId: externalTrx !== undefined ? externalTrx : v4()
            };
            const transactionInfo: OrderInfo = await getEntityPlayerFinanceService().transferOut(entity, data);
            const responseStatus = transactionInfo.isNew ? 201 : 200;
            delete transactionInfo.isNew;
            res.status(responseStatus).send(transactionInfo);
        } catch (err) {
            next(err);
        }
    });

router.put("/:path/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getBrand(req);
            const service = getPlayerService();
            const reason: string = req.query.reason;
            const playerInfo: PlayerInfo = await service.suspend(entity, req.params.playerCode, reason);
            res.send(playerInfo);
        } catch (err) {
            next(err);
        }
    });

router.delete("/:path/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getBrand(req);
            await getBrandPlayerValidator().validatePlayerCanUnblock(req.params.playerCode, entity);
            const service = getPlayerService();
            const playerInfo: PlayerInfo = await service.restore(entity, req.params.playerCode);
            res.send(playerInfo);
        } catch (err) {
            next(err);
        }
    });

router.delete("/:path/players/:playerCode/login-lock",
    authenticate,
    authorize,
    auditable,
    unlockPlayer);

router.post("/:path/players/group/status",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getBrand(req);
            const service = getPlayerService();
            await service.changeStatusByIds(entity, req.body);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export const createUserUnderPath = async (req: Request & PermissionsHolder,
                                          res: express.Response,
                                          next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const service = getUserService(entity);
        const createData: CreateUserData = req.body;
        createData.editorEntity = req.keyEntity;
        const userInfo: UserInfo = await service.create(req.body);
        res.status(201).send(userInfo);
        next();
    } catch (err) {
        next(err);
    }
};

/**
 * Create new user
 */
router.post("/:path/users", authenticate, authorize, CREATE_USER_VALIDATOR, auditable, createUserUnderPath);

/**
 * Get user creation options
 */
router.get("/:path/users/create-options", authenticate, authorize, getUserCreateOptions);

router.delete("/:path/users/:username", authenticate, authorize, auditable, deleteUserHandler);

/**
 * Change user email
 *
 */

router.post("/:path/users/:username/email/force-set",
    authenticate,
    authorize,
    validateUsername,
    validate({ email: { notEmpty: true, isEmail: true } }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            res.send(await UserService.forceChangeEmail(entity, req.params["username"], req.body));
            next();
        } catch (err) {
            next(err);
        }

    });

/**
 * Change user password
 *
 */

router.post("/:path/users/:username/password",
    authenticate,
    authorize,
    validateUsername,
    validateChangePasswordValidator,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            res.send(await getUserPasswordService(entity).changePassword(req.params["username"], req.body));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Unlock user after reaching max count of invalid login
 *
 */

router.delete("/:path/users/:username/login-lock",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    unlockUser);

/**
 * Unlock user password change after reaching max count of invalid password changing requests
 *
 */

router.delete("/:path/users/:username/change-password-lock",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    unlockChangingPasswordUser);

router.post("/:path/users/:username/password/force-reset",
    authenticate,
    authorize,
    validateUsername,
    resetPasswordValidator,
    auditable,
    resetPasswordHandler);

/**
 * Change status of user to suspended
 *
 */
router.put("/:path/users/:username/suspended",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            const service = getUserService(entity);
            res.send(await service.suspend(req.params["username"]));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change status of user to normal
 *
 */
router.delete("/:path/users/:username/suspended",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            const service = getUserService(entity);
            res.send(await service.restore(req.params["username"]));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Change type of user by path
 *
 */
router.patch("/:path/users/:username/type",
    authenticate,
    authorize,
    CHANGE_USER_TYPE_VALIDATOR,
    changeUserType
);

router.post("/:path/users/group/status",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            await UserService.updateStatuses(entity, req.body);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

/**
 * Get user details
 *
 */
router.get("/:path/users/:username", authenticate, authorize, validateUsername, getUserDetails);

/**
 * Get user details with blocking info
 *
 */
router.get("/:path/users/:username/profile",
    authenticate,
    authorize,
    validateUsername,
    getUserProfileWithBlocking);

/**
 * Get list of users for given tree part
 *
 */
router.get("/:path/users",
    authenticate,
    authorize,
    defineLimits,
    getUserValidator,
    decodePid({ ignoredKeys: ["customDataRoleId__in", "customDataRoleId__in!"] }),
    getUser
);

export const patchUserUnderPath = async (req: Request & PermissionsHolder,
                                         res: express.Response,
                                         next: express.NextFunction) => {
    const username: string = req.params.username;

    try {
        const entity = getEntity(req);
        const service = getUserService(entity);
        const updateData: UpdateUserData = req.body;
        updateData.editorEntity = req.keyEntity;
        const user: UserInfo = await service.update(username, req.body);
        res.send(user);
    } catch (err) {
        next(err);
    }
};

/**
 * Edit user details
 */
router.patch("/:path/users/:username", authenticate, authorize, CHANGE_USER_VALIDATOR, auditable, patchUserUnderPath);

router.get("/:path/balances",
    authenticate,
    authorize,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req) as ChildEntity;
            res.send(await entity.fetchBalances());
        } catch (err) {
            next(err);
        }
    });

router.get("/:path/payments/methods",
    authenticate,
    authorize,
    validate({
        type: { notEmpty: true, isAlpha: true },
    }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const type: string = req.query.type;
        try {
            const entity: BaseEntity = req.keyEntity.find({ path: getEntityPath(req) });

            if (!entity) {
                return next(new Errors.EntityCouldNotBeFound());
            }
            const paymentMethods: PaymentMethodInfo[] = await PaymentMethodService.find(entity, { type: type });
            res.send(paymentMethods).end();
        } catch (err) {
            next(err);
        }
    }
);

router.post("/:path/payments/methods",
    authenticate,
    authorize,
    validate({
        code: { notEmpty: true, isWord: true },
        type: { notEmpty: true },
        name: { notEmpty: true, isWord: true },
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const entity: BaseEntity = req.keyEntity.find({ path: getEntityPath(req) });
        try {
            if (!entity) {
                return next(new Errors.EntityCouldNotBeFound());
            }

            const data: PaymentMethodCreateData = req.body;
            data.brandId = entity.id;

            const info = await PaymentMethodService.create(data);
            res.send(info).end();
        } catch (err) {
            next(err);
        }
    }
);

router.patch("/:path/payments/methods/:code",
    authenticate,
    authorize,
    validate({
        code: { notEmpty: true },
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const entity: BaseEntity = req.keyEntity.find({ path: getEntityPath(req) });
        try {
            if (!entity) {
                return next(new Errors.EntityCouldNotBeFound());
            }

            const code: string = req.params.code;
            const data: PaymentMethodCreateData = req.body;

            const info = await PaymentMethodService.update(entity, code, data);
            res.send(info).end();
        } catch (err) {
            next(err);
        }
    }
);

router.post("/:path/payments/deposits",
    authenticate,
    authorize,
    validate({
        paymentMethodCode: { notEmpty: true, isWord: true },
        customerId: { notEmpty: true, isWord: true },
        currency: { notEmpty: true },
    }),
    auditable,
    paymentDepositsHandler
);

router.post("/:path/payments/withdrawals",
    authenticate,
    authorize,
    validate({
        paymentMethodCode: { notEmpty: true, isWord: true },
        customerId: { notEmpty: true, isWord: true },
        currency: { notEmpty: true },
    }),
    auditable,
    paymentWithdrawalsHandler
);

router.put("/:path/payments/publickey",
    authenticate,
    authorize,
    validate({
        publicKey: { notEmpty: true },
    }),
    auditable,
    paymentSetPublicKeyHandler
);

router.get("/:path/payments/gateways/publickey",
    authenticate,
    authorize,
    paymentGetPublicKeyHandler
);

router.post("/:path/payments/gateways/publickey",
    authenticate,
    authorize,
    auditable,
    paymentGeneratePublicKeyHandler
);

/**
 * Set WebSiteWhitelistedCheck flag.
 */
export async function setWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
                                                 next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await EntityService.updateWebSiteWhitelistedCheck(entity, req.body.level);

        res.status(200).end();
    } catch (err) {
        next(err);
    }
}

/**
 * Get WebSiteWhitelistedCheck flag.
 */
export async function getWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
                                                 next: express.NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        res.send({ level: entity.inheritedWebSiteWhitelistedCheck });
    } catch (err) {
        next(err);
    }
}

/**
 * Resets WebSiteWhitelistedCheck flag.
 */
export async function resetWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
                                                   next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await EntityService.resetWebSiteWhitelistedCheck(entity);

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

router.patch("/:path/check-website-whitelisted", authenticate, authorize, auditable, setWebSiteWhitelistedCheck);
router.get("/:path/check-website-whitelisted", authenticate, authorize, getWebSiteWhitelistedCheck);
router.delete("/:path/check-website-whitelisted", authenticate, authorize, auditable, resetWebSiteWhitelistedCheck);

export async function updateEntityStatus(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        res.send(await EntityService.updateEntityStatus(req.keyEntity, { path: getEntityPath(req) }, req.body.status));
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Change status for entity (deprecated)
 *
 */
router.patch("/:path/status", authenticate, authorize, isMasterEntity, validate({
    status: { isAvailableEntityStatus: true }
}), auditable, updateEntityStatus);

router.get("/:path/domain/:domain",
    authenticate,
    authorize,
    findByDomain);

router.post("/:path/search-by-key",
    authenticate,
    authorize,
    auditable,
    findByKeys);

export default router;

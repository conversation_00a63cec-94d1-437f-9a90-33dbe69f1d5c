import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { addHeaderCacheControl, createRequestLogger, notFound<PERSON><PERSON><PERSON>, resolveIp } from "../../bootstrap/fastify";
import logger, { ILogger } from "../../utils/logger";
import health from "./health.router";
import version from "./version.router";
import { IpHolder } from "../../services/security";
import Translation, { TranslateHolder } from "@skywind-group/sw-management-i18n";
import { measures } from "@skywind-group/sw-utils";
import { buildLogData, getRequestInfoFomRequest } from "../../utils/requestHelper";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { handleError } from "../middleware/errorMiddleware";
import config from "../../config";

const log = logger("routers");

function createFastifyErrorHandler(logger: ILogger) {
    return (err: any,
            req: FastifyRequest & IpHolder,
            reply: FastifyReply & TranslateHolder): any => {
        const reqData = buildLogData(req.url, req.method, req.query, req.body, getRequestInfoFomRequest(req));

        measures.measureProvider.saveError(err);

        if (reply.sent) {
            logger.warn(err, { reqData }, "HTTP headers have already been sent", reqData);
            return;
        }

        if (err) {
            const error = handleError(err, logger);
            reply.code(error.responseStatus)
                .send({
                    ...Translation.getErrorResponse(error as SWError, reply),
                    translateMessage: false
                });
        }
    };
}

export async function defineRoutes(app: FastifyInstance,
                                   define: (app: FastifyInstance) => Promise<void>): Promise<FastifyInstance> {

    app.addHook("preHandler", resolveIp);
    app.addHook("preHandler", createRequestLogger(log));
    app.addHook("onRequest", addHeaderCacheControl);
    app.addHook(
        "onRequest",
        (req: FastifyRequest, rep: FastifyReply, done: () => void) => allowHTTPMethods(req, rep, done)
    );

    await define(app);
    app.register(health, { prefix: "/v1" });
    app.register(version, { prefix: "/v1" });

    app.setErrorHandler(createFastifyErrorHandler(log));
    app.setNotFoundHandler(notFoundHandler);

    return app;
}

function allowHTTPMethods(request: FastifyRequest,
                          reply: FastifyReply,
                          done: () => void) {
    const allowedMethods = new Set(config.allowedHTTPMethods);
    const method = request.method.toUpperCase();
    reply.header("Access-Control-Allow-Methods", config.allowedHTTPMethods.join(","));
    if (!allowedMethods.has(method)) {
        reply.code(405).send({
            statusCode: 405,
            message: `${method} not allowed.`,
            error: "Method Not Allowed"
        })
    }
    done();
}

import * as express from "express";
import { NextFunction, Response } from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    encodePublicIdWrapper,
    getBooleanParamFromQuery,
    getBrand,
    getEntity,
    pagingWrapper,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { validateGameCode } from "./entityGame";
import { MAX_INT_VALUE, VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { BaseEntity } from "../entities/entity";
import { getGameGroupGameService, getGameGroupLimitService, getGameGroupService } from "../services/gamegroup";
import { GameGroupData, GameGroupInfo } from "../entities/gamegroup";
import { getEntitySettings } from "../services/settings";
import { gameGroupFilterService } from "../services/gamegroup/filter";
import { prepareScheme } from "../services/filter";
import { getParentIds } from "../services/entity";
import { GAME_CODE_IN_PATTERN, GAME_CODE_PATTERN } from "../entities/game";

const validateGameGroupLimitsList = {
    gameGroup: { isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] } },
    gameGroup__in: { optional: true, isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH * 20 }] } },
    gameCode__in: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH * 20 }] },
        matches: { options: GAME_CODE_IN_PATTERN }
    },
    gameCode: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
        matches: { options: GAME_CODE_PATTERN }
    },
    gameType: { optional: true, isGameType: true },
    offset: { optional: true, isInt: { options: { min: 0, max: MAX_INT_VALUE } } },
    limit: { optional: true, isInt: { options: { min: 0, max: 200 } } },
    sortOrder: { optional: true, isSortOrder: true },
    isCustomLimitsSupported: { optional: true, isBoolean: true }
};

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

const validateGameGroupName = validate({
    gameGroup: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } }
});

const validateNewGameGroupName = validate({
    gameGroup: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
    newName: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } }
});

export const DEFAULT_SORT_KEY = "createdAt";
export const DEFAULT_SORT_ORDER = "DESC";

async function getGameGroups(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const settings = await getEntitySettings(entity.path);
        const groups: GameGroupInfo[] = await getGameGroupService()
            .findAll(entity, settings, req.query);
        res.send(pagingWrapper(encodePublicIdWrapper(groups), req, res));
    } catch (err) {
        next(err);
    }
}

async function createGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const data: GameGroupData = req.body;
        const gameGroup = await getGameGroupService().create(entity,
            data);
        res.status(201).send(gameGroup);
    } catch (err) {
        next(err);
    }
}

async function updateGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().update(entity, req.params["gameGroup"], req.body);
        res.send(gameGroup);
    } catch (err) {
        next(err);
    }
}

async function removeGameGroup(req: Request, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupService().delete(entity,
            req.params.gameGroup, getBooleanParamFromQuery(req.query, "force", false));
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getGamesInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupGameService().findAll(entity,
            req.params["gameGroup"]);
        res.send(gameGroup);
    } catch (err) {
        next(err);
    }
}

async function renameGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().rename(entity,
            req.params.gameGroup, req.body.newName, req.query.force);
        res.send(gameGroup);
    } catch (err) {
        next(err);
    }
}

async function setDefaultGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().setDefault(entity,
            req.params["gameGroup"]);
        res.send(gameGroup);
    } catch (err) {
        next(err);
    }
}

async function addGameToGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupGameService().add(entity,
            req.params["gameGroup"],
            req.params["gameCode"],
            req.body,
            getBooleanParamFromQuery(req.query, "overrideDefault"));
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function getGameLimitsInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        res.send(await getGameGroupLimitService().findOne(entity,
            req.params["gameGroup"], req.params["gameCode"]));
    } catch (err) {
        next(err);
    }
}

async function getAllGameLimitsForEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        res.send(await getGameGroupLimitService().findAll(entity,
            req.query));
    } catch (err) {
        next(err);
    }
}

async function deleteGameLimitsInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupGameService().delete(entity,
            req.params["gameGroup"], req.params["gameCode"]);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function updateGameLimitsInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupLimitService().update(entity,
            req.params["gameGroup"],
            req.params["gameCode"],
            req.body,
            getBooleanParamFromQuery(req.query, "overrideDefault"));

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getGameLimitsByCurrencyInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        res.send(await getGameGroupLimitService().findByCurrency(entity,
            req.params["gameGroup"],
            req.params["gameCode"],
            req.params["currency"]));
    } catch (err) {
        next(err);
    }
}

async function updateGameLimitsByCurrencyInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupLimitService().updateByCurrency(entity,
            req.params["gameGroup"],
            req.params["gameCode"],
            req.params["currency"],
            req.body);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function removeGameLimitsByCurrencyInGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getBrand(req);
        await getGameGroupLimitService().removeByCurrency(
            entity,
            req.params.gameGroup,
            req.params.gameCode,
            req.params.currency);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getGameGroupFiltersByEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const brandIds = [entity.id];

        const settings = await getEntitySettings(entity.path);
        if (settings.gameGroupsInheritance) {
            brandIds.push(...getParentIds(entity));
        }

        const items = await gameGroupFilterService.get().findGameGroupFilters({
            query: req.query,
            brandIds,
            entity,
            settings
        })
        res.send(items);
    } catch (err) {
        next(err);
    }
}

async function createGameGroupFilter(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().findOne(entity, { name: req.params.gameGroup }, true);
        const result = await gameGroupFilterService.get().create({ ...req.body, groupId: gameGroup.get("id") });

        res.send(result.toInfo());
    } catch (err) {
        next(err);
    }
}

async function getGameGroupFilters(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().findOne(entity, { name: req.params.gameGroup }, true);
        const items = await gameGroupFilterService.get().list({ where: { groupId: gameGroup.get("id") } });

        res.send(items.map(item => item.toInfo()));
    } catch (err) {
        next(err);
    }
}

async function updateGameGroupFilter(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const gameGroup = await getGameGroupService().findOne(entity, { name: req.params.gameGroup }, true);
        const result = await gameGroupFilterService.get().update(req.params.filterId, {
            ...req.body,
            groupId: gameGroup.get("id")
        });

        res.send(result.toInfo());
    } catch (err) {
        next(err);
    }
}

async function deleteGameGroupFilter(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        await getGameGroupService().findOne(entity, { name: req.params.gameGroup }, true);
        await gameGroupFilterService.get().destroy(req.params.filterId);

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

/**
 * Gets set of GameGroup of brand
 */
router.get(
    "/gamegroups",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset"])),
    getGameGroups
);

router.get(
    "/entities/:path/gamegroups",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset"])),
    getGameGroups
);

/**
 * Creates GameGroup for brand
 */
router.post("/gamegroups", authenticate, authorize,
    validate({ name: { notEmpty: true } }), auditable, createGameGroup);
router.post("/entities/:path/gamegroups",
    authenticate,
    authorize,
    validate({ name: { notEmpty: true } }),
    auditable,
    createGameGroup);

router.get("/gamegroups/limits",
    authenticate,
    authorize,
    decodePid(),
    validate(validateGameGroupLimitsList),
    getAllGameLimitsForEntity);

router.get("/entities/:path/gamegroups/limits",
    authenticate,
    authorize,
    decodePid(),
    validate(validateGameGroupLimitsList),
    getAllGameLimitsForEntity);

/**
 * Game group filters by entity
 */

router.get("/gamegroups/filters",
    authenticate, authorize, validate(prepareScheme(["limit", "offset"])), getGameGroupFiltersByEntity);
router.get("/entities/:path/gamegroups/filters",
    authenticate, authorize, validate(prepareScheme(["limit", "offset"])), getGameGroupFiltersByEntity);
/**
 * Update GameGroup for brand
 */
router.patch("/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    validate({
        description: { notEmpty: true }
    }),
    auditable,
    updateGameGroup);
router.patch("/entities/:path/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    validate({
        description: { notEmpty: true }
    }),
    auditable,
    updateGameGroup);

/**
 * Remove GameGroup for brand
 */
router.delete("/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    auditable,
    removeGameGroup);
router.delete("/entities/:path/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    auditable,
    removeGameGroup);

/**
 * Game group filter
 */

router.post("/gamegroups/:gameGroup/filters",
    authenticate, authorize, auditable, createGameGroupFilter);
router.get("/gamegroups/:gameGroup/filters",
    authenticate, authorize, getGameGroupFilters);
router.put("/gamegroups/:gameGroup/filters/:filterId",
    authenticate, authorize, decodePid(), auditable, updateGameGroupFilter);
router.delete("/gamegroups/:gameGroup/filters/:filterId",
    authenticate, authorize, decodePid(), auditable, deleteGameGroupFilter);

router.post("/entities/:path/gamegroups/:gameGroup/filters",
    authenticate, authorize, auditable, createGameGroupFilter);
router.get("/entities/:path/gamegroups/:gameGroup/filters",
    authenticate, authorize, getGameGroupFilters);
router.put("/entities/:path/gamegroups/:gameGroup/filters/:filterId",
    authenticate, authorize, decodePid(), auditable, updateGameGroupFilter);
router.delete("/entities/:path/gamegroups/:gameGroup/filters/:filterId",
    authenticate, authorize, decodePid(), auditable, deleteGameGroupFilter);

/**
 * Gets list of games in  brand's GameGroup
 */
router.get("/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    getGamesInGameGroup);
router.get("/entities/:path/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validateGameGroupName,
    getGamesInGameGroup);

/**
 *  Rename existing game group
 */
router.put("/gamegroups/:gameGroup/rename",
    authenticate,
    authorize,
    validateNewGameGroupName,
    auditable,
    renameGameGroup);
router.put("/entities/:path/gamegroups/:gameGroup/rename",
    authenticate,
    authorize,
    validateNewGameGroupName,
    auditable,
    renameGameGroup);
/*
 *  Set existing game group as default
 */
router.put("/gamegroups/:gameGroup/default",
    authenticate,
    authorize,
    auditable,
    setDefaultGameGroup);
router.put("/entities/:path/gamegroups/:gameGroup/default",
    authenticate,
    authorize,
    auditable,
    setDefaultGameGroup);

/**
 *  Adds game to game group and specify limits
 */
router.put("/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    addGameToGameGroup);
router.put("/entities/:path/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    addGameToGameGroup);

/**
 *  Gets limits of the game in the game group
 */
router.get("/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    getGameLimitsInGameGroup);

router.get("/entities/:path/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    getGameLimitsInGameGroup);

/**
 *  Deletes limits of the game in the game group
 */
router.delete("/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    deleteGameLimitsInGameGroup);
router.delete("/entities/:path/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    deleteGameLimitsInGameGroup);

/**
 *  Updates limits of the game in the game group
 */
router.patch("/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    updateGameLimitsInGameGroup);
router.patch("/entities/:path/gamegroups/:gameGroup/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    updateGameLimitsInGameGroup);

/**
 *  Gets limits by currency code of the game in the game group
 */
router.get("/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    getGameLimitsByCurrencyInGameGroup);
router.get("/entities/:path/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    getGameLimitsByCurrencyInGameGroup);

/**
 *  Updates limits by currency code of the game in the game group
 */
router.put("/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    updateGameLimitsByCurrencyInGameGroup);
router.put("/entities/:path/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    updateGameLimitsByCurrencyInGameGroup);
/**
 * Deletes limits by currency code of the game in game group
 */
router.delete("/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    removeGameLimitsByCurrencyInGameGroup);

router.delete("/entities/:path/gamegroups/:gameGroup/games/:gameCode/limits/:currency",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    removeGameLimitsByCurrencyInGameGroup);

export default router;

import { NextFunction, Request, Response, Router } from "express";
import { KeyEntityHolder } from "../../services/security";
import * as EntityService from "../../services/entity";
import EntityCache from "../../cache/entity";
import { Merchant } from "../../entities/merchant";
import * as MerchantService from "../../services/merchant";
import { BrandEntity } from "../../entities/brand";
import { authenticateInternal } from "../middleware/middleware";
import { getEntitySettings } from "../../services/settings";
import { getEntityJurisdictionService } from "../../services/entityJurisdiction";
import { MerchantEntityInfo } from "@skywind-group/sw-wallet-adapter-core";

const router: Router = Router();

router.get("/merchantentities/:type/:code",
    authenticateInternal,
    getMerchantEntityInfoByTypeAndCode
);

router.post("/entities/info",
    authenticateInternal,
    entitiesInfo
);

router.get("/merchantentities/:type/:code/settings/",
    authenticateInternal,
    getMerchantEntitySettingsByTypeAndCode
);

async function entitiesInfo(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = await EntityService.findOne({ key: req.body.key });
        const result = await entity.toInfo();
        result["path"] = entity.path;
        res.status(200).send(result);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getMerchantEntityInfoByTypeAndCode(req: Request & KeyEntityHolder,
                                                         res: Response,
                                                         next: NextFunction) {
    try {
        const merchantTypeStr: string = req.params.type;
        const merchantType: string | string[] = merchantTypeStr.includes(",")
            && merchantTypeStr.split(",").map(t => t.trim()) || merchantTypeStr;

        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(merchantType, req.params.code);

        const brand: BrandEntity = await EntityCache.findById<BrandEntity>(merchant.brandId);

        const entityInfo = await brand.toInfo() as MerchantEntityInfo;
        entityInfo.merchant = merchant.toInfo();
        entityInfo.path = brand.path;

        const [jurisdiction] = await getEntityJurisdictionService().findAll({ entityId: merchant.brandId });
        if (jurisdiction) {
            entityInfo.jurisdictionCode = jurisdiction.code;
        }

        res.status(200).send(entityInfo);
        next();
    } catch (err) {
        next(err);
    }
}

async function getMerchantEntitySettingsByTypeAndCode(req: Request & KeyEntityHolder,
                                                      res: Response,
                                                      next: NextFunction) {
    try {
        const merchantTypeStr: string = req.params.type;
        const merchantType: string | string[] = merchantTypeStr.includes(",")
            && merchantTypeStr.split(",").map(t => t.trim()) || merchantTypeStr;
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(merchantType, req.params.code);
        const brand: BrandEntity = await EntityCache.findById<BrandEntity>(merchant.brandId);
        const entitySettings = await getEntitySettings(brand.path);
        res.status(200).send(entitySettings);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;

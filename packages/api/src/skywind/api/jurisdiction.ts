import * as express from "express";
import { auditable, authenticate, authorize, decodePid, getEntity, validate } from "./middleware/middleware";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { getJurisdictionService } from "../services/jurisdiction";
import { AllowedJackpotConfigurationLevel, Jurisdiction } from "../entities/jurisdiction";
import * as Errors from "../errors";
import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { prepareScheme } from "../services/filter";
import * as EntityJurisdictionCache from "../cache/entityJurisdiction";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

/**
 * Gets all jurisdictions for current brand
 */
router.get("/jurisdictions",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset"])),
    async (req: Request,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            let jurisdictions: Jurisdiction[];
            const limit = req.query.limit;
            const offset = req.query.offset;
            if (req.keyEntity.isMaster()) {
                jurisdictions = await getJurisdictionService().findAll({}, limit, offset);
            } else {
                jurisdictions = await getEntityJurisdictionService().findAll({
                    entityId: req.keyEntity.id
                }, limit, offset);
            }

            res.send(jurisdictions);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets a jurisdiction by id
 */
router.get("/jurisdictions/:jurisdictionCode",
    authenticate,
    authorize,
    decodePid(),
    async (req: Request,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            let jurisdiction: Jurisdiction;
            if (req.keyEntity.isMaster()) {
                jurisdiction = await getJurisdictionService().findOne(req.params.jurisdictionCode);
            } else {
                jurisdiction = await getEntityJurisdictionService()
                    .findOne(req.keyEntity.id, req.params.jurisdictionCode);
            }
            res.send(jurisdiction);
        } catch (err) {
            next(err);
        }
    });

/**
 * Creates jurisdiction
 */
router.post("/jurisdictions",
    authenticate,
    authorize,
    validate({
        title: {
            optional: true,
            isLength: {
                options: [{ max: VARCHAR_DEFAULT_LENGTH }],
                errorMessage: `Max length - ${VARCHAR_DEFAULT_LENGTH} chars`
            }
        },
        allowedCountries: {
            optional: true,
            isCountryCode: true
        },
        restrictedCountries: {
            optional: true,
            isCountryCode: true
        },
        defaultCountry: {
            optional: true,
            isCountryCode: { options: { isSingleValue: true } }
        },
        allowedJackpotConfigurationLevel: {
            optional: true,
            isAllowedJackpotConfigurationLevel: {
                options: { allowNull: true },
                errorMessage: "Allowed values: " +
                    Object.values(AllowedJackpotConfigurationLevel).filter(el => typeof el === "number")
            }
        }
    }),
    auditable,
    async (req: Request & UserInfoHolder,
           res: express.Response,
           next: express.NextFunction) => {

        if (!req.keyEntity.isMaster()) {
            next(new Errors.NotMasterEntityError());
            return;
        }

        try {
            const jurisdiction = await getJurisdictionService().create({
                ...req.body, createdUserId: req.userId, updatedUserId: req.userId
            });

            res.status(201).send(jurisdiction);
        } catch (err) {
            next(err);
        }
    });

/**
 * Updates a jurisdiction
 */
router.patch("/jurisdictions/:jurisdictionCode",
    authenticate,
    authorize,
    decodePid(),
    validate({
        title: {
            optional: true,
            isLength: {
                options: [{ max: VARCHAR_DEFAULT_LENGTH }],
                errorMessage: `Max length - ${VARCHAR_DEFAULT_LENGTH} chars`
            }
        },
        allowedCountries: {
            optional: true,
            isCountryCode: { options: { allowNull: true } }
        },
        restrictedCountries: {
            optional: true,
            isCountryCode: { options: { allowNull: true } }
        },
        defaultCountry: {
            optional: true,
            isCountryCode: { options: { allowNull: true, isSingleValue: true } }
        },
        allowedJackpotConfigurationLevel: {
            optional: true,
            isAllowedJackpotConfigurationLevel: {
                options: { allowNull: false },
                errorMessage: "Allowed values: " +
                    Object.values(AllowedJackpotConfigurationLevel).filter(el => typeof el === "number")
            }
        }
    }),
    auditable,
    async (req: Request & UserInfoHolder,
           res: express.Response,
           next: express.NextFunction) => {

        if (!req.keyEntity.isMaster()) {
            next(new Errors.NotMasterEntityError());
            return;
        }

        try {
            const jurisdiction = await getJurisdictionService().update(req.params.jurisdictionCode,
                {
                    ...req.body,
                    updatedUserId: req.userId
                });

            res.send(jurisdiction);
        } catch (err) {
            next(err);
        }
    });

/**
 * Marks jurisdiction as archived
 */
router.delete("/jurisdictions/:jurisdictionCode",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request & UserInfoHolder,
           res: express.Response,
           next: express.NextFunction) => {

        if (!req.keyEntity.isMaster()) {
            next(new Errors.NotMasterEntityError());
            return;
        }

        try {
            await getJurisdictionService().remove(req.params.jurisdictionCode);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

// Entity jurisdictions section

/**
 * Gets current entity jurisdictions
 */
router.get("/entities/:path/jurisdictions",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset"])),
    async (req: Request,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
            const jurisdictions = await getEntityJurisdictionService().findAll({
                entityId: entity.id
            }, req.query.limit, req.query.offset);
            res.send(jurisdictions);
        } catch (err) {
            next(err);
        }
    });

/**
 * Adds jurisdiction to entity
 */
router.put("/entities/:path/jurisdictions/:jurisdictionCode",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request & UserInfoHolder,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            const jurisdiction = await getEntityJurisdictionService().add(entity, req.params.jurisdictionCode);
            EntityJurisdictionCache.reset(entity.id.toString());
            res.send(jurisdiction);
        } catch (err) {
            next(err);
        }
    });

/**
 * Marks jurisdiction as archived
 */
router.delete("/entities/:path/jurisdictions/:jurisdictionCode",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request & UserInfoHolder,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            await getEntityJurisdictionService().remove(entity, req.params.jurisdictionCode);
            EntityJurisdictionCache.reset(entity.id.toString());
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;

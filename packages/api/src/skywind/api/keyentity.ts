import * as express from "express";
import { NextFunction, Response } from "express";
import { v4 } from "uuid";
import {
    auditable,
    authenticate,
    authorize,
    currencyValidator,
    decodePid,
    defineLimits,
    defineLimitsWithBalance,
    FORMAT_CSV,
    FormattedResponse,
    getBooleanParamFromRequestQuery,
    getBrand,
    getEntity,
    sanitizeBoolean,
    sanitizeCommaSeparatedString,
    validate,
    validateBody,
    validateChangePasswordValidator
} from "./middleware/middleware";

import { getUserAuthService } from "../services/user/userAuth";
import { DetailedUserInfo, User, UserInfo, UserWithBlockingInfo } from "../entities/user";
import { BrandEntity } from "../entities/brand";
import { hasPermissions, KeyEntityHolder, PermissionsHolder, UserInfoHolder } from "../services/security";
import {
    CreateData as CreatePlayerData,
    default as getPlayerService,
    getBrandPlayerService,
    queryParamsKeys as brandPlayerQueryParamsKeys,
    UpdateData as UpdatePlayerData
} from "../services/brandPlayer";
import { getBrandPlayerValidator } from "../services/brandPlayerValidator";
import * as UserService from "../services/user/user";
import getUserService, { CreateData, UpdateData } from "../services/user/user";
import * as Errors from "../errors";
import { parseFilter, prepareScheme } from "../services/filter";
import { Player } from "../entities/player";
import { BaseEntity, ENTITY_TYPE, EntityInfo, EntityShortInfo } from "../entities/entity";
import {
    InitializeDepositData,
    InitializePaymentInfo,
    InitializeWithdrawalData,
    PaymentMethodCreateData,
    PaymentMethodInfo
} from "../entities/payment_method";
import * as PaymentService from "../services/payment";
import { getEntityPlayerFinanceService } from "../services/payment";
import { validatePlayerCodeForOperator, validatePlayerInfo, validatePlayerUpdate } from "./playerAuthForBrands";
import getStructureService from "../services/entityStructure";
import { normalizeCurrencyAmount } from "./payment";
import { OrderInfo, TransferData } from "../entities/payment";
import { getPaymentGatewayService } from "../services/paymentGatewayService";
import * as Settings from "../services/settings";
import EntitySettingsService from "../services/settings";
import { mergeObjectArray, VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { getUserPasswordService } from "../services/user/userPassword";
import { PlayerBulkService } from "../services/bulk/playerBulkService";
import { ProxyService } from "../services/proxy";
import * as EntityService from "../services/entity";
import { addMerchantCodes, findEntitiesByDomain, findEntityByKeys, getShortInfoOfParents } from "../services/entity";
import { getAvailableSiteService } from "../services/availableSites";
import EntityCache from "../cache/entity";
import { getPlayerInfoService, queryParamsKeys as playerInfoQueryParamsKeys } from "../services/playerInfo";
import { ValidateNickname } from "../utils/validateNickname";
import { getRefererDomain } from "./login";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

export const validateUsername = validate({
    username: { notEmpty: true, isWord: true }
});

export const CHANGE_USER_TYPE_VALIDATOR = validate({
    userType: {
        optional: false,
        hasUserType: true
    }
});

export const CREATE_USER_VALIDATOR = validate({
    username: {
        notEmpty: true,
        isWord: true,
        isLength: { options: [{ min: 4, max: VARCHAR_DEFAULT_LENGTH }] },
    },
    firstName: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
    },
    lastName: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
    },
    password:
    {
        notEmpty: true,
        isPassword: true,
        errorMessage: "Password did not pass complexity check",
    },
    email: { optional: true, isEmail: true },
    phone: { optional: true, isAuthPhoneNumber: true, errorMessage: "Invalid phone number. Should be +xxxxx" },
    forcePasswordChangePeriodType: { optional: true, isTimeIntervalType: true },
    forcePasswordChangePeriod: { optional: true, isInt: { options: { min: 1 } } }
});

export const CHANGE_USER_VALIDATOR = validate({
    username: {
        "in": "body",
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
        isWord: true
    },
    firstName: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
    },
    lastName: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
    },
    status: { optional: true, isStatus: true },
    phone: { optional: true, isAuthPhoneNumber: true, errorMessage: "Invalid phone number. Should be +xxxxx" },
    userType: {
        optional: true,
        hasUserType: true
    },
    forcePasswordChangePeriodType: { optional: true, isTimeIntervalType: true },
    forcePasswordChangePeriod: { optional: true, isInt: { options: { min: 1 } } }
});

export const getUserValidator = validate({
    ...prepareScheme(["limit", "offset", "sortOrder", "status"]),
    fields: {
        optional: true,
        isCommaSeparatedPermissibleFields: {
            options: {
                fields: ["firstName", "lastName", "username", "email"]
            }
        }
    }
});

export const validateMoveEntity = validate({
    entityKey: { notEmpty: true },
    newParentKey: { notEmpty: true }
});

router.get("/structure", authenticate, authorize, sanitizeBoolean("includeProxy", "includeMerchantCode"), getStructure);

export async function getStructure(req: Request & PermissionsHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const structureInfo: EntityInfo = await entity.structureToInfo({
            decryptId: hasPermissions(req, ["id:decode"])
        });

        if (req.query.includeProxy) {
            await (new ProxyService()).includeTo(structureInfo);
        }

        if (req.query.includeJurisdiction === "true") {
            await getEntityJurisdictionService().includeTo(structureInfo);
        }

        if (req.query.includeMerchantCode) {
            await addMerchantCodes(structureInfo);
        }

        res.send(structureInfo);
    } catch (err) {
        next(err);
    }
}

export const validateShortStructure = validate({
    additionalFields: {
        optional: true,
        isCommaSeparatedPermissibleFields: {
            options: {
                fields: [
                    "dynamicDomainId",
                    "staticDomainId",
                    "environment",
                    "defaultCountry",
                    "defaultCurrency",
                    "defaultLanguage"
                ]
            }
        }
    }
});

router.get("/short-structure",
    authenticate,
    authorize,
    validateShortStructure,
    sanitizeCommaSeparatedString("additionalFields"),
    getShortStructure);

export async function getShortStructure(req: Request & PermissionsHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const structureInfo: EntityShortInfo = await entity.structureToShortInfo({
            decryptId: hasPermissions(req, ["id:decode"]),
            additionalFields: req.query.additionalFields
        });

        if (req.query.includeJurisdiction === "true") {
            await getEntityJurisdictionService().includeTo(structureInfo as EntityInfo);
        }

        res.send(structureInfo);
    } catch (err) {
        next(err);
    }
}

router.get("/short-structure/search",
    authenticate,
    authorize,
    searchInShortStructure);

export async function searchInShortStructure(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const userService = getUserService(entity);
        const user = await userService.findOne(req.username);

        const isSuperAdmin = user.hasSuperAdminRole() && entity.isMaster();
        if (!isSuperAdmin) {
            throw new Errors.ValidationError("It is forbidden to search: user is not superadmin");
        }

        const shortInfo: EntityShortInfo[] = [];
        let entityIds: number[] = [];

        if (req.query.url || req.query.url__contains) {
            const sites = await getAvailableSiteService(entity)
                .list({ where: parseFilter(req.query, ["url"]) });
            entityIds = sites.map(site => site.entityId);
        }

        for (const id of entityIds) {
            const brand = await EntityCache.findById(id);
            shortInfo.push(getShortInfoOfParents(brand));
        }

        res.send(mergeObjectArray(shortInfo)[0]);
    } catch (err) {
        next(err);
    }
}

router.post("/structure/move-entity", authenticate, authorize, validateMoveEntity, moveEntity);

export async function moveEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const user: User = await getUserService(entity).findOne(req.username);
        await getStructureService().rearrangeChildren(entity, req.body, user);
        res.status(200).end();
    } catch (err) {
        if (err instanceof Errors.EntityHasConflictsWithParent) {
            res.status(err.responseStatus).json({
                code: err.code,
                message: err.message,
                conflicts: err.data.conflicts
            });
            next();
        } else {
            next(err);
        }
    }
}

router.get("/brief",
    authenticate,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entitySettings = await Settings.getEntitySettings(req.keyEntity.path, true);
            const entityInfo = await req.keyEntity.toInfo();
            res.send({
                ...entityInfo,
                settings: entitySettings
            }).end();
        } catch (err) {
            next(err);
        }
    });

router.get("/users/:username/permissions",
    authenticate,
    authorize,
    validateUsername,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = getUserService(req.keyEntity);
            res.send(await service.getPermissionList(req.params["username"]));
            next();
        } catch (err) {
            next(err);
        }
    });

export const validateCreatePlayer = validateBody({
    code: {
        notEmpty: { errorMessage: "should be not empty" },
        isWord: true
    },
    password: { optional: true },
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: { errorMessage: "invalid format" } },
    status: { isStatus: true, optional: true },
    isVip: { optional: true, isBoolean: true }
});

router.post("/players",
    authenticate,
    authorize,
    validateCreatePlayer,
    createPlayer);

export const validateRegisterPlayer = validateBody({
    code: {
        notEmpty: { errorMessage: "should be not empty" },
        isWord: true
    },
    password: { notEmpty: true },
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: { errorMessage: "invalid format" } },
    status: { isStatus: true, optional: true },
    isVip: { optional: true, isBoolean: true }
});

router.post("/players/register",
    authenticate,
    authorize,
    validateRegisterPlayer,
    auditable,
    createPlayer);

export async function createPlayer(req: Request, res: Response, next: NextFunction) {
    const data: CreatePlayerData = req.body;
    try {
        const entity = getBrand(req);
        const player = await getPlayerService().create(entity, data);
        res.status(201).send(await player.toInfo());
    } catch (err) {
        next(err);
    }
}

export const validateLimitOffsetStatus = validate(prepareScheme(["limit", "offset", "sortOrder", "status"]));

router.get("/players",
    authenticate,
    authorize,
    validateLimitOffsetStatus,
    defineLimits,
    defineLimitsWithBalance,
    getPlayers);

export async function getPlayers(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const isCSV = req.query.format === FORMAT_CSV;
        const withBalance = !isCSV || isCSV && req.query.withBalance === "true";
        const players = await getBrandPlayerService().search(
            req.keyEntity as BrandEntity,
            parseFilter(req.query, brandPlayerQueryParamsKeys),
            withBalance,
            getBooleanParamFromRequestQuery(req, "withoutGameGroup")
        );
        res.sendFormatted(req, players, ["customData"]);
    } catch (err) {
        next(err);
    }
}

router.get("/players/info",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    defineLimits,
    getPlayersInfo);

export async function getPlayersInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getBrand(req);
        const playersInfo = await getPlayerInfoService()
            .find(parseFilter(req.query, playerInfoQueryParamsKeys), entity.id);
        res.send(playersInfo);
    } catch (err) {
        next(err);
    }
}

router.get("/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getPlayer);

export async function getPlayer(req: Request, res: Response, next: NextFunction) {
    if (!req.keyEntity.isBrand()) {
        return next(new Errors.NotBrand());
    }
    const playerCode: string = req.params.playerCode;
    const entity: BrandEntity = req.keyEntity as BrandEntity;
    try {
        const fetchAuditData = getBooleanParamFromRequestQuery(req, "withAudit");
        const withLastAction = getBooleanParamFromRequestQuery(req, "withLastAction");

        const player: Player = await getBrandPlayerService().findOneExtended(entity,
            { code: playerCode },
            fetchAuditData,
            withLastAction);
        res.send(await player.toInfoWithBalances());
    } catch (err) {
        next(err);
    }
}

router.post("/players/bulk-operation",
    authenticate,
    authorize,
    decodePid({
        keysToParse: ["item"]
    }),
    auditable,
    playerBulkHandler);

async function playerBulkHandler(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const service = new PlayerBulkService();
        const result = await service.process(entity, req.body);
        res.status(201).send(result);
    } catch (err) {
        next(err);
    }
}

router.patch("/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerUpdate,
    auditable,
    patchPlayer);

export async function patchPlayer(req: Request, res: Response, next: NextFunction) {
    const playerCode: string = req.params.playerCode;
    const data: UpdatePlayerData = req.body;
    try {
        const entity = getBrand(req);
        const player = await getPlayerService().update(entity, playerCode, data);
        res.send(await player.toInfo());
    } catch (err) {
        next(err);
    }
}

router.put("/players/:playerCode/gamegroups/:gameGroup",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        if (!req.keyEntity.isBrand()) {
            return next(new Errors.NotBrand());
        }
        const entity: BrandEntity = req.keyEntity as BrandEntity;
        try {
            const player = await getBrandPlayerService().updateGameGroup(entity,
                { code: req.params["playerCode"] },
                req.params["gameGroup"]);
            res.send(player);
        } catch (err) {
            next(err);
        }
    });

export const validatePlayerFinance = validate({
    currency: currencyValidator,
    amount: { notEmpty: true, isDecimal: true, isPositive: true },
    exTrxId: { optional: true, isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] } }
});

router.post("/players/:playerCode/deposits/:currency/:amount",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validatePlayerFinance,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const externalTrx: string = req.body.exTrxId;
        const entity = req.keyEntity;
        if (!entity.isBrand()) {
            return next(new Errors.NotBrand());
        }
        try {
            const data: TransferData = {
                playerCode: req.params["playerCode"],
                currency: req.params.currency,
                amount: normalizeCurrencyAmount(req.params.currency, req.params.amount),
                extTrxId: externalTrx !== undefined ? externalTrx.toString() : v4()
            };

            const transactionInfo: OrderInfo =
                await getEntityPlayerFinanceService().transferIn(entity as BrandEntity, data);
            const responseStatus = transactionInfo.isNew ? 201 : 200;
            delete transactionInfo.isNew;
            res.status(responseStatus).send(transactionInfo);
        } catch (err) {
            next(err);
        }
    });

router.post("/players/:playerCode/withdrawals/:currency/:amount",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validatePlayerFinance,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const externalTrx: string = req.body.exTrxId;
        const entity = req.keyEntity;
        if (!entity.isBrand()) {
            return next(new Errors.NotBrand());
        }
        try {
            const data: TransferData = {
                playerCode: req.params["playerCode"],
                currency: req.params.currency,
                amount: normalizeCurrencyAmount(req.params.currency, req.params.amount),
                extTrxId: externalTrx !== undefined ? externalTrx : v4()
            };
            const transactionInfo: OrderInfo =
                await getEntityPlayerFinanceService().transferOut(entity as BrandEntity, data);
            const responseStatus = transactionInfo.isNew ? 201 : 200;
            delete transactionInfo.isNew;
            res.status(responseStatus).send(transactionInfo);
        } catch (err) {
            next(err);
        }
    });

router.put("/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    putSuspend);

router.delete("/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    deleteSuspend);

export async function putSuspend(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getBrand(req);
        const service = getPlayerService();
        const reason = req.query.reason;
        const playerInfo = await service.suspend(entity, req.params.playerCode, reason);
        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
}

export async function deleteSuspend(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getBrand(req);
        await getBrandPlayerValidator().validatePlayerCanUnblock(req.params.playerCode, entity);
        const service = getPlayerService();
        const playerInfo = await service.restore(entity, req.params.playerCode);
        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
}

router.post("/players/group/status",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getBrand(req);
            const service = getPlayerService();
            await service.changeStatusByIds(entity, req.body);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

router.delete("/players/:playerCode/login-lock",
    authenticate,
    authorize,
    auditable,
    unlockPlayer);

export async function unlockPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        res.send(await getPlayerService().unlockLogin(entity, req.params["playerCode"]));
    } catch (err) {
        next(err);
    }
}

router.post("/users", authenticate, authorize, auditable, CREATE_USER_VALIDATOR, createUser);

export async function createUser(req: Request & PermissionsHolder, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);

        const createData: CreateData = req.body;
        createData.editorEntity = req.keyEntity;
        const canEditRoles: boolean = (await getUserService(req.keyEntity).findOne(req.username)).hasSuperAdminRole();

        const service = getUserService(entity, canEditRoles);
        const userInfo: UserInfo = await service.create(req.body);
        res.status(201).send(userInfo);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/users/create-options", authenticate, authorize, getUserCreateOptions);

export async function getUserCreateOptions(
    req: Request & PermissionsHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const settingsService = new EntitySettingsService(entity);
        const settings = await settingsService.get();
        const userCreateOptions = {
            passwordForceChangePeriod: settings.passwordForceChangePeriod
        };
        res.status(200).send(userCreateOptions);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/users",
    authenticate,
    authorize, getUserValidator,
    decodePid({ ignoredKeys: ["customDataRoleId__in", "customDataRoleId__in!"] }),
    defineLimits,
    getUser
);

export async function getUser(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        res.sendFormatted(req, await UserService.search(
            req.keyEntity,
            entity,
            parseFilter(req.query, UserService.queryParamsKeys, true), req.query.customData),
            ["grantedPermissions"]);
    } catch (err) {
        next(err);
    }
}

/**
 * Change user email
 *
 */

router.post("/users/:username/email/force-set",
    authenticate,
    authorize,
    validateUsername,
    validate({ email: { notEmpty: true, isEmail: true } }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await UserService.forceChangeEmail(req.keyEntity, req.params["username"], req.body));
            next();
        } catch (err) {
            next(err);
        }

    });

export function raiseErrorIfNotPermittedToDeleteUser(req: Request, entity: BaseEntity) {
    if (entity.key === req.keyEntity.key && req.username === req.params.username) {
        throw new Errors.ValidationError("You cannot delete yourself");
    }
}

export const deleteUserHandler = async (req: Request, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = getEntity(req);

        raiseErrorIfNotPermittedToDeleteUser(req, entity);

        await getUserService(entity).remove(req.params.username);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
};

router.delete("/users/:username", authenticate, authorize, auditable, deleteUserHandler);

router.post("/users/:username/password",
    authenticate,
    authorize,
    validateChangePasswordValidator,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await getUserPasswordService(req.keyEntity).changePassword(req.params["username"], req.body));
            next();
        } catch (err) {
            next(err);
        }

    });

export const resetPasswordValidator = validate({
    newPassword: {
        notEmpty: { errorMessage: "Password should be not empty" },
        isLength: {
            options: [{ max: VARCHAR_DEFAULT_LENGTH }], errorMessage: `Max length - ${VARCHAR_DEFAULT_LENGTH} chars`
        }
    },
});

export const resetPasswordHandler = async (req: Request, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const user: UserInfo = await getUserPasswordService(entity)
            .forceChangePassword(req.params.username, req.body.newPassword);
        res.send(user);
    } catch (err) {
        next(err);
    }
};

router.post("/users/:username/password/force-reset",
    authenticate,
    authorize,
    validateUsername,
    resetPasswordValidator,
    auditable,
    resetPasswordHandler);

router.delete("/users/:username/login-lock",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    unlockUser);

export async function unlockUser(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await getUserAuthService(entity).unlockLogin(req.params["username"]);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

router.delete("/users/:username/change-password-lock",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    unlockChangingPasswordUser);

export async function unlockChangingPasswordUser(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await getUserPasswordService(entity).unlockChangingPassword(req.params["username"]);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

router.delete("/users/:ip/reset-password-lock",
    authenticate,
    authorize,
    validate({
        ip: { notEmpty: true }
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = getEntity(req);
            await getUserPasswordService(entity).unlockResetPassword(req.params["ip"]);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

router.put("/users/:username/suspended",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = getUserService(req.keyEntity);
            res.send(await service.suspend(req.params["username"]));
            next();
        } catch (err) {
            next(err);
        }

    });

router.delete("/users/:username/suspended",
    authenticate,
    authorize,
    validateUsername,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = getUserService(req.keyEntity);
            res.send(await service.restore(req.params["username"]));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/users/group/status",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            await UserService.updateStatuses(req.keyEntity, req.body);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

router.post("/users/email",
    authenticate,
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const username: string = req.username;
        const domain = getRefererDomain(req, true);
        let user: UserInfo;
        try {
            if (req.body.token && req.body.newEmail) {
                user = await UserService.changeEmail(req.keyEntity, username, {
                    ...req.body,
                    domain
                });
            } else {
                user = await UserService.requestChangeEmail(req.keyEntity, username, domain);
            }

            res.send(user);
        } catch (err) {
            next(err);
        }
    }
);

router.post("/users/email/confirmation",
    authenticate,
    validate({
        token: { notEmpty: true },
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const username: string = req.username;
        const token: string = req.body.token;
        try {
            const user: UserInfo = await UserService.confirmEmail(req.keyEntity, username, token);
            res.send(user);
        } catch (err) {
            next(err);
        }
    }
);

router.get("/payments/methods",
    authenticate,
    authorize,
    validate({
        type: { notEmpty: true, isAlpha: true },
    }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const type: string = req.query.type;
        try {
            const paymentMethods: PaymentMethodInfo[] = await PaymentService.find(req.keyEntity, { type: type });
            res.send(paymentMethods).end();
        } catch (err) {
            next(err);
        }
    }
);

router.post("/payments/methods",
    authenticate,
    authorize,
    validate({
        code: { notEmpty: true },
        type: { notEmpty: true },
        name: { notEmpty: true },
    }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const data: PaymentMethodCreateData = req.body;
        data.brandId = req.keyEntity.id;
        try {
            const info = await PaymentService.create(data);
            res.send(info).end();
        } catch (err) {
            next(err);
        }

    }
);

router.patch("/payments/methods/:code",
    authenticate,
    authorize,
    validate({
        code: { notEmpty: true },
    }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const code: string = req.params.code;
        const data: PaymentMethodCreateData = req.body;

        try {
            const info = await PaymentService.update(req.keyEntity, code, data);
            res.send(info).end();
        } catch (err) {
            next(err);
        }

    }
);

router.post("/payments/deposits",
    authenticate,
    authorize,
    validate({
        paymentMethodCode: { notEmpty: true, isWord: true },
        customerId: { notEmpty: true, isWord: true },
        currency: { notEmpty: true },
    }),
    auditable,
    paymentDepositsHandler
);

export async function paymentDepositsHandler(req: Request, res: express.Response, next: express.NextFunction) {
    const data: InitializeDepositData = req.body;
    try {
        const entity = getEntity(req);

        const player = await getBrandPlayerService().findOne(entity as BrandEntity, { code: data.customerId });

        if (player.currency !== data.currency) {
            return next(new Errors.CurrencyMismatch());
        }

        const service = getPaymentGatewayService();
        const info: InitializePaymentInfo = await service.deposit(entity, data);
        res.send(info).end();
    } catch (err) {
        next(err);
    }
}

router.post("/payments/withdrawals",
    authenticate,
    authorize,
    validate({
        paymentMethodCode: { notEmpty: true, isWord: true },
        customerId: { notEmpty: true, isWord: true },
        currency: { notEmpty: true },
    }),
    auditable,
    paymentWithdrawalsHandler
);

export async function paymentWithdrawalsHandler(req: Request, res: express.Response, next: express.NextFunction) {
    const data: InitializeWithdrawalData = req.body;
    try {
        const entity = getEntity(req);
        await getBrandPlayerService().findOne(entity as BrandEntity, { code: data.customerId });
        const service = getPaymentGatewayService();
        const info: InitializePaymentInfo = await service.withdraw(entity, data);
        res.send(info).end();
    } catch (err) {
        next(err);
    }
}

router.put("/payments/publickey",
    authenticate,
    authorize,
    validate({
        publicKey: { notEmpty: true },
    }),
    paymentSetPublicKeyHandler
);

export async function paymentSetPublicKeyHandler(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);

        const publicKey: string = req.body.publicKey;
        await getPaymentGatewayService().setPublicKey(entity, publicKey);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

router.get("/payments/gateways/publickey",
    authenticate,
    authorize,
    paymentGetPublicKeyHandler
);

export async function paymentGetPublicKeyHandler(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);

        const publicKey: string = await getPaymentGatewayService().getPublicKey(entity);
        res.send({ publicKey: publicKey }).end();
    } catch (err) {
        next(err);
    }
}

router.post("/payments/gateways/publickey",
    authenticate,
    authorize,
    paymentGeneratePublicKeyHandler
);

export async function paymentGeneratePublicKeyHandler(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);

        const publicKey: string = await getPaymentGatewayService().generatePublicKey(entity);
        res.send({ publicKey: publicKey }).end();
    } catch (err) {
        next(err);
    }
}

router.patch("/users/:username", authenticate, authorize, CHANGE_USER_VALIDATOR, auditable, patchUserDetails);

export async function patchUserDetails(req: Request & PermissionsHolder,
    res: express.Response,
    next: express.NextFunction) {
    const username: string = req.params.username;

    try {
        const entity = getEntity(req);
        const updateData: UpdateData = req.body;
        updateData.editorEntity = req.keyEntity;
        const canEditRoles: boolean = (await getUserService(req.keyEntity).findOne(req.username)).hasSuperAdminRole();

        const service = getUserService(entity, canEditRoles);
        const user: UserInfo = await service.update(username, updateData);
        res.send(user);
    } catch (err) {
        next(err);
    }
}

/**
 * Get user details
 *
 */
router.get("/users/:username", authenticate, authorize, validateUsername, getUserDetails);

export async function getUserDetails(req: Request, res: express.Response, next: express.NextFunction) {
    const username: string = req.params.username;

    try {
        const entity = getEntity(req);
        const service = getUserService(entity);
        const user: DetailedUserInfo = (await service.findOne(username)).toDetailedInfo();
        res.send(user);
    } catch (err) {
        next(err);
    }
}

/**
 * Change type of user
 *
 */
export async function changeUserType(req: Request & PermissionsHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = getUserService(entity);
        res.send(await service.changeUserTypeByPath(req.params["username"], req.body["userType"]));
        next();
    } catch (err) {
        next(err);
    }
}

router.patch("/users/:username/type",
    authenticate,
    authorize,
    CHANGE_USER_TYPE_VALIDATOR,
    changeUserType
);

/**
 * Get user details with blocking info
 *
 */
router.get("/users/:username/profile",
    authenticate,
    authorize,
    validateUsername,
    getUserProfileWithBlocking);

export async function getUserProfileWithBlocking(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const username: string = req.params.username;
        const entity: BaseEntity = getEntity(req);

        const service = getUserService(entity);
        const user: UserWithBlockingInfo = await (await service.findOne(username)).toProfileInfo(entity);
        res.send(user);
    } catch (err) {
        next(err);
    }
}

router.get("/balances",
    authenticate,
    authorize,
    getBalances);

export async function getBalances(req: Request, res: Response, next: NextFunction) {
    const keyEntity = req.keyEntity;
    try {
        const balances = await keyEntity.fetchBalances();
        res.send(balances);
    } catch (err) {
        next(err);
    }
}

/**
 * Set WebSiteWhitelistedCheck flag.
 */
export async function setWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await EntityService.updateWebSiteWhitelistedCheck(entity, req.body.level);

        res.status(200).end();
    } catch (err) {
        next(err);
    }
}

/**
 * Get WebSiteWhitelistedCheck flag.
 */
export async function getWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        res.send({ level: entity.inheritedWebSiteWhitelistedCheck });
    } catch (err) {
        next(err);
    }
}

/**
 * Resets WebSiteWhitelistedCheck flag.
 */
export async function resetWebSiteWhitelistedCheck(req: Request & KeyEntityHolder, res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await EntityService.resetWebSiteWhitelistedCheck(entity);

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

router.get("/domain/:domain",
    authenticate,
    authorize,
    findByDomain);

router.post("/search-by-key",
    authenticate,
    authorize,
    findByKeys);

export async function findByDomain(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const info = await findEntitiesByDomain(req.keyEntity, req.params.domain);
        res.send(info);
        next();
    } catch (err) {
        next(err);
    }
}

export async function findByKeys(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const info = await findEntityByKeys(req.keyEntity, req.body);
        res.send(info);
        next();
    } catch (err) {
        next(err);
    }
}

router.patch("/check-website-whitelisted", authenticate, authorize, setWebSiteWhitelistedCheck);
router.get("/check-website-whitelisted", authenticate, authorize, getWebSiteWhitelistedCheck);
router.delete("/check-website-whitelisted", authenticate, authorize, resetWebSiteWhitelistedCheck);

router.get("/players/validate-nickname/:playerNickname",
    authenticate,
    authorize,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const validator = new ValidateNickname();
            await validator.checkSymbols(req.params.playerNickname);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    }
);

router.put("/players/info",
    authenticate,
    authorize,
    validatePlayerInfo,
    createOrUpdatePlayersInfo);

export async function createOrUpdatePlayersInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getBrand(req);
        if (!entity.isMerchant) {
            await getBrandPlayerService().findOne(entity, { code: req.body.playerCode });
        }
        const playerInfo = await getPlayerInfoService().createOrUpdate({
            playerCode: req.body.playerCode,
            brandId: entity.id,
            isMerchantPlayer: entity.isMerchant,
            ...req.body
        });
        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
}

router.patch(
    ["/player/change-nickname", "/entities/:path/player/change-nickname"],
    authenticate,
    authorize,
    changeNickname
);

export async function changeNickname(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const { code, nickname } = req.body;
        const entity = getEntity(req);
        await getPlayerInfoService().createOrUpdate({
            playerCode: code,
            brandId: entity.id,
            nickname,
            isMerchantPlayer: entity.type === ENTITY_TYPE.MERCHANT,
        }, { increaseNicknameChangeAttempts: false });
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

export default router;

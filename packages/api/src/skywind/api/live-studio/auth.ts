import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { generateLiveStudioToken } from "../../utils/token";

export default function (router: FastifyInstance, options, done) {
    router.post("/token", generateToken);
    done();
}

export async function generateToken(req: FastifyRequest & Request, res: Response) {
    const liveStudioToken = await generateLiveStudioToken({});
    return res.send({ liveStudioToken });
}

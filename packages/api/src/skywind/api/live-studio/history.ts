import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { parseFilter, } from "../../services/filter";
import { setupDatesRangeLimits } from "../../utils/datesHelper";
import config from "../../config";
import { findAllEntityGameInfos } from "../../services/game";
import * as HistoryService from "../../history/gameHistory";
import { RoundHistory } from "../../entities/gameHistory";
import { parseLiveStudioToken } from "../../services/security";
import { LIVE_STUDIO_TOKEN } from "./validators";
import { getSpinHistoryByRound } from "../../history/spinHistory";
import { BrandEntity } from "../../entities/brand";
import EntityCache from "../../cache/entity";
import { GAME_TYPES } from "../../utils/common";
import * as Errors from "../../errors";
import {
    GameHistoryRoundParams,
    GameHistorySpinDetailsParams,
    GameHistorySpinDetailsQuery,
    LiveStudioTokenHeaders
} from "./definition";

export default function (router: FastifyInstance, options, done) {
    router.get("/history/game", getGameHistory);
    router.get("/history/game/:roundId/events/:eventId", getGameHistorySpinDetails);
    router.get("/history/game/:roundId", getGameHistoryRound);
    done();
}

export async function getGameHistory(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const brand = await EntityCache.findById(+req.query["brandId"]);
        if (!brand) {
            throw new Errors.EntityCouldNotBeFound();
        }
        if (config.limits.isPeriodLimitEnabled) {
            // last 3 month by default
            const minDate = new Date();
            minDate.setMonth(minDate.getMonth() - config.limits.periodLimit);
            setupDatesRangeLimits(req.query as any, { diffInMs: (new Date()).getTime() - minDate.getTime() });
        }

        const queries = new Map();
        queries.set("game", { ...parseFilter(req.query, ["code", "title"]), type: GAME_TYPES.live });
        const games = await findAllEntityGameInfos(brand, queries, true);

        req.query["gameCode__in"] = games
            .map(game => game.code)
            .join(",");

        const rounds = await HistoryService.findGameHistoryEntries(
            brand.id, parseFilter(req.query, HistoryService.queryParamsKeys)
        );
        const updatedRounds: (RoundHistory & { gameName: string })[] = [];

        for (const round of rounds) {
            const game = games.find(({ code }) => code === round.gameCode);
            updatedRounds.push({
                ...round,
                gameName: game && game.title
            });
        }

        return res.send(updatedRounds);
    } catch (err) {
        throw err;
    }
}

export async function getGameHistorySpinDetails(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Querystring: GameHistorySpinDetailsQuery,
    Params: GameHistorySpinDetailsParams
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const brand = await EntityCache.findById(+req.query.brandId);
        if (!brand) {
            throw new Errors.EntityCouldNotBeFound();
        }
        const spinDetails = await HistoryService.getGameHistoryDetails(
            brand,
            +req.params.roundId,
            +req.params.eventId,
            {
                playerCode: req.query.playerCode,
                addJurisdictionSettings: true
            }
        );
        return res.send(spinDetails);
    } catch (err) {
        throw err;
    }
}

export async function getGameHistoryRound(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Params: GameHistoryRoundParams
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const brand = await EntityCache.findById<BrandEntity>(+req.query["brandId"]);
        if (!brand) {
            throw new Errors.EntityCouldNotBeFound();
        }
        const historyLines = await getSpinHistoryByRound(brand, +req.params.roundId, req.query);
        return res.send(historyLines);
    } catch (err) {
        throw err;
    }
}

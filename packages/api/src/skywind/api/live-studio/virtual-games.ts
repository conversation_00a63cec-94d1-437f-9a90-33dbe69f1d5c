import { LIVE_STUDIO_TOKEN } from "./validators";
import { FastifyInstance, FastifyRequest } from "fastify";
import * as GameProviderService from "../../services/gameprovider";
import { EntityGameInfo, Game, LiveProviderSettings } from "../../entities/game";
import { Request, Response } from "../../bootstrap/fastify";
import { parseLiveStudioToken } from "../../services/security";
import * as GameService from "../../services/game";
import EntityCache from "../../cache/entity";
import { PagingInfo } from "../../utils/paginghelper";
import * as UrlManager from "../../services/urlManager";
import { LiveStudioTokenHeaders, UpdateGameSettingsBody } from "./definition";

export default function (router: FastifyInstance, options, done) {
    router.post("/game/:type", registerGame);
    router.patch("/game/:gameCode", updateGame);
    router.post("/virtual-games", getGames);
    router.patch("/game/:gameCode/activate", enableGame);
    router.patch("/game/:gameCode/deactivate", disableGame);
    router.delete("/game/:gameCode", disableGame);
    router.get("/game/:gameCode", getGame);
    router.get("/game/:gameCode/url/fun", getFunGameUrl);
    router.get("/game/:gameCode/checkExist", isGameExist);
    router.patch("/game-video-settings/:tableId", updateGameVideoSettings);
    router.patch("/game-settings", updateGameSettings);
    router.delete("/table-in-games/:tableId", deleteTableIdInGames);
    router.get("/game-details/:tableId", getGameDetails);
    done();
}

export async function registerGame(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Body: GameProviderService.RegisterGameData,
    Params: { type: string },
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const data: GameProviderService.RegisterGameData = req.body;
        data.type = req.params.type;
        const game: Game = await GameProviderService.register({ ...data });

        return res.status(201).send(game.toCodeInfo());
    } catch (err) {
        throw err;
    }
}

export async function updateGame(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCode: string = req.params["gameCode"];
        const data: GameProviderService.UpdateGameData = req.body;
        const game: Game = await GameProviderService.update(gameCode, { ...data });
        return res.send(game.toCodeInfo());
    } catch (err) {
        throw err;
    }
}

export async function getGames(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const entity = await EntityCache.findOne({ path: ":" });
        const body = { ...req.body as any, includeSuspendedGames: true };
        const gameInfoList: EntityGameInfo[] = await GameService.getAllLiveGames(entity, body);
        if (gameInfoList) {
            const info: PagingInfo = gameInfoList["PAGING_INFO"];
            const total: number = Array.isArray(info.total) ? info.total.length : info.total;
            res.header("x-paging-total", total.toString());
            res.header("x-paging-limit", info.limit.toString());
            res.header("x-paging-offset", info.offset.toString());
        }
        return res.send(gameInfoList);
    } catch (err) {
        throw err;
    }
}

export async function disableGame(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCode: string = req.params["gameCode"];

        const game: Game = await GameProviderService.disableGame(gameCode);
        return res.send(game.toCodeInfo());
    } catch (err) {
        throw err;
    }
}

export async function enableGame(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCode: string = req.params["gameCode"];

        const game: Game = await GameProviderService.enableGame(gameCode);
        return res.send(game.toCodeInfo());
    } catch (err) {
        throw err;
    }
}

export async function getGame(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCode: string = req.params["gameCode"];
        const game: Game =
            await GameProviderService.getGame(gameCode, {});
        return res.send(game.toInfo());
    } catch (err) {
        throw err;
    }
}

export async function getFunGameUrl(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Querystring: { [key: string]: any }
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const entity = await EntityCache.findOne({ path: ":" });
        const gameCode: string = req.params["gameCode"];
        const playerGameURL = await UrlManager.getAnonymousGameURL({
            keyEntity: entity,
            gameCode,
            ip: req.query.ip,
            language: req.query.language,
            ticket: req.query.ticket,
            merchantLoginUrl: req.query.merchantLoginUrl,
            lobby: req.query.lobby,
            cashier: req.query.cashier,
            gameGroup: req.query.gameGroup,
            currency: req.query.currency
        });
        return res.send(playerGameURL);
    } catch (err) {
        throw err;
    }
}

export async function isGameExist(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCode: string = req.params["gameCode"];
        const isExists = await GameService.isGameExists(gameCode);
        return res.send({ exists: isExists });
    } catch (err) {
        throw err;
    }
}

export async function updateGameVideoSettings(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const data: LiveProviderSettings = req.body;
        const gameCodes = await GameProviderService.updateAllLiveGamesVideoSetting(+req.params["tableId"], data);
        return res.status(200).send({ gameCodes });
    } catch (err) {
        throw err;
    }
}

export async function deleteTableIdInGames(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const gameCodes = await GameProviderService.removeTableIdInGames(+req.params["tableId"]);
        return res.status(200).send({ gameCodes });
    } catch (err) {
        throw err;
    }
}

export async function updateGameSettings(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Body: UpdateGameSettingsBody
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const data = req.body;
        await GameProviderService.updateLiveGamesSetting(data.gameCodes, data.settings);
        return res.status(204).send();
    } catch (err) {
        throw err;
    }
}

export async function getGameDetails(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    try {
        const tableId: string = req.params["tableId"];
        return res.send(await GameService.findGameCodesByTableId(tableId));
    } catch (err) {
        throw err;
    }
}

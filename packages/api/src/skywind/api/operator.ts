import { auditLogin, finishAuditSession, loginRefreshUser, loginUser, logoutUser, validateLoginUser } from "./login";
import {
    auditable,
    authenticate,
    authorize,
    convertDatesToISOMiddleware,
    decodePid,
    defineDatesRangeLimitsForRoundHistory,
    defineDatesRangeLimitsMiddleware,
    defineIsLiveGameFlagFalse,
    defineLimits,
    getEntity,
    sanitize,
    sanitizeBoolean,
    sanitizeSearchingILIKE,
    setMerchantAuthContext,
    setPaymentDateIfNotPresentInRequest,
    validate,
    validatePromoPermissionsPerType
} from "./middleware/middleware";
import { NextFunction, Request, Response, Router } from "express";
import {
    CHANGE_USER_VALIDATOR,
    changeNickname,
    CREATE_USER_VALIDATOR,
    createPlayer,
    createUser,
    deleteSuspend,
    deleteUserHandler,
    getBalances,
    getPlayer,
    getPlayers,
    getShortStructure,
    getStructure,
    getUser,
    getUserDetails,
    getUserValidator,
    moveEntity,
    patchPlayer,
    patchUserDetails,
    putSuspend,
    unlockChangingPasswordUser,
    unlockUser,
    validateCreatePlayer,
    validateLimitOffsetStatus,
    validateMoveEntity,
    validateRegisterPlayer,
    validateUsername
} from "./keyentity";

import { getLanguages } from "./language";
import {
    addGamesToEntity,
    customDefineLimits,
    getFunGameByCode,
    getGameByCode,
    getGamesInfo,
    getGamesInfoSearch,
    removeGamesFromEntity,
    sanitizeLanguage,
    updateGamesLimits,
    validateGameCode,
    validateGameGroupName,
    validateGroupLimitFiltersUpdate,
    validateJpCurrency,
    validateLanguage,
    validateLimitAndOffset,
    validatePlayMode,
    validateSegmentId
} from "./entityGame";

import {
    checkMailIsTaken,
    checkPlayerCodeIsTaken,
    setPlayerPasswordIfEmpty,
    updatePlayerPassword,
    validateEmail,
    validatePlayerCodeForOperator,
    validateSetPlayerPassword,
    validateUpdatePlayerPassword
} from "./playerAuthForBrands";
import {
    directTransfersIn,
    directTransfersOut,
    getTransactionInfo,
    payments,
    transfersIn,
    transfersOut,
    validateTransfers
} from "./payment";
import {
    getGameHistory,
    getGameHistoryDetailsImage,
    getGameHistoryRound,
    getUnfinishedRounds,
    validateFields,
    validateOptionalSpinNumber,
    validateRoundId,
    validateSearch,
    validateSpinNumber
} from "./history";
import { getCurrencyReportRequestHandler, playerReports, validateCurrencyFilter, validateFilter, } from "./report";
import {
    brandJackpotContributionReportHandler,
    getJPContributionLogsHandler,
    getJPWinLogsHandler,
    jpLogsValidator,
    playersJackpotContributionReportHandler,
    validateCurrency,
    validateDateHour,
    validateOffsetAndLimit
} from "./reportJackpot";
import { prepareScheme } from "../services/filter";
import {
    addFreebetsToPlayerFromPromo,
    addFreebetsToPlayers,
    validateBulkCreate
} from "./promotions/playerFreebetPromotion";
import {
    createPromo,
    deletePromo,
    getPromoById,
    getPromos,
    patchPromo,
    validateGetPromoSchema,
    validatePatchPromoSchema,
    validatePostPromoSchema
} from "./promotions/promotion";
import {
    addPromotionToPlayers,
    addPromotionToPlayersByPlayersCodes,
    getPlayerPromotion,
    getPlayerPromotionFreeBetLeft,
    getPlayerPromotions,
    getPromotionPlayers,
    revokePromoFromPlayer,
    validatePlayersCodes
} from "./promotions/playerPromotion";
import { validateMail } from "./site";
import {
    createMerchantEntity,
    getMerchantEntityInfo,
    getMerchantGameUrl,
    getMerchantLobbyUrl,
    updateMerchantEntity,
    validateCreateMerchantEntityData,
    validateUpdateMerchantEntityData
} from "./merchant";
import { killSession } from "./playerSession";
import {
    createEntity,
    createUserUnderPath,
    deleteEntity,
    getEntityDetails,
    patchUserUnderPath,
    updateEntity,
    validateEntityCreation,
    validateEntityUpdate,
} from "./entity";
import { createBrand, validateBrand } from "./brand";
import { getChildrenRolesList, getRolesList, validateSearchByTitle } from "./role";
import { getEntityGameService } from "../services/entityGameService";
import { KeyEntityHolder } from "../services/security";
import { getCountries } from "./country";
import { getCurrencies } from "./currency";
import { PROMO_OPERATIONS } from "../entities/promotion";

const mung = require("express-mung");

const router: Router = Router();

router.post("/login",
    validateLoginUser,
    mung.json(auditLogin),
    loginUser);

router.post("/logout",
    authenticate,
    auditable,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["userId"] }),
    mung.headers(finishAuditSession),
    logoutUser);

router.post("/login/refresh",
    authenticate,
    auditable,
    loginRefreshUser);

router.delete("/users/:username/login-lock",
    authenticate,
    authorize,
    validateUsername,
    unlockUser);

router.delete("/entities/:path/users/:username/login-lock",
    authenticate,
    authorize,
    validateUsername,
    unlockUser);

router.delete("/entities/:path/users/:username/change-password-lock",
    authenticate,
    authorize,
    validateUsername,
    unlockChangingPasswordUser);

router.delete("/users/:username/change-password-lock",
    authenticate,
    authorize,
    validateUsername,
    unlockChangingPasswordUser);

router.get("/countries",
    authenticate,
    authorize,
    getCountries);

router.get("/currencies",
    authenticate,
    authorize,
    getCurrencies);

router.get("/languages",
    authenticate,
    authorize,
    getLanguages);

router.get("/balances",
    authenticate,
    authorize,
    getBalances);

router.get(["/games/:gameCode/info", "/entities/:path/games/:gameCode/info"],
    authenticate,
    authorize,
    validateGameCode,
    validateJpCurrency,
    validateGameGroupName,
    validateSegmentId,
    getGamesInfo);

router.get("/games/info/search",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validateLimitAndOffset,
    validateJpCurrency,
    validateCurrency,
    getGamesInfoSearch);

router.post("/players",
    authenticate,
    authorize,
    validateCreatePlayer,
    createPlayer);

router.post("/players/register",
    authenticate,
    authorize,
    validateRegisterPlayer,
    createPlayer);

router.put("/players/:playerCode/password",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateUpdatePlayerPassword,
    updatePlayerPassword);

router.post("/players/:playerCode/password/set-for-unpassworded",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateSetPlayerPassword,
    setPlayerPasswordIfEmpty);

router.get("/players",
    authenticate,
    authorize,
    validateLimitOffsetStatus,
    defineLimits,
    getPlayers);

router.get("/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getPlayer);

router.patch("/players/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateEmail,
    patchPlayer);

router.put("/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    putSuspend);

router.delete("/players/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    deleteSuspend);

router.get("/players/:playerCode/games/:gameCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateGameCode,
    sanitizeLanguage,
    validateLanguage,
    getGameByCode);

router.get("/fun/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    getFunGameByCode);

router.post("/payments/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    transfersIn);

router.post("/payments/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    transfersOut);

router.get("/payments",
    authenticate,
    authorize,
    defineLimits,
    convertDatesToISOMiddleware(["startDate", "endDate"]),
    defineDatesRangeLimitsMiddleware(["startDate"], false),
    payments);

router.get("/payments/:transactionId",
    authenticate,
    authorize,
    getTransactionInfo);

router.get("/history/game",
    authenticate,
    authorize,
    validateSearch,
    defineLimits,
    decodePid({ forceReturnIfNumber: true }),
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["firstTs", "ts"]),
    defineDatesRangeLimitsForRoundHistory(["firstTs"], "ts", "finished"),
    getGameHistory);

router.get("/history/game/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateOptionalSpinNumber,
    validateFields("sortOrder", "type"),
    getGameHistoryRound);

router.get("/entities/:path/history/game",
    authenticate,
    authorize,
    validateSearch,
    defineLimits,
    decodePid({ forceReturnIfNumber: true }),
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["firstTs", "ts"]),
    defineDatesRangeLimitsForRoundHistory(["firstTs"], "ts", "finished"),
    getGameHistory);

router.get("/entities/:path/history/game/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateOptionalSpinNumber,
    validateFields("sortOrder", "type"),
    getGameHistoryRound);

router.get("/history/game/:roundId/details/:spinNumber/image",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateSpinNumber,
    getGameHistoryDetailsImage);

router.get("/report/currency",
    authenticate,
    authorize,
    validateCurrencyFilter,
    defineLimits,
    getCurrencyReportRequestHandler
);

router.get("/report/players",
    authenticate,
    authorize,
    validateFilter,
    setPaymentDateIfNotPresentInRequest,
    defineLimits,
    sanitizeSearchingILIKE,
    playerReports);

router.get("/report/jackpot/contributions",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    brandJackpotContributionReportHandler);

router.get("/report/jackpot/contributions/players",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    validateCurrency,
    validate(prepareScheme(["offset", "limit", "dateHour", "currency"])),
    playersJackpotContributionReportHandler);

router.get("/report/jackpot/contributions/logs",
    authenticate,
    authorize,
    jpLogsValidator,
    getJPContributionLogsHandler);

router.get("/report/jackpot/contributions/wins",
    authenticate,
    authorize,
    jpLogsValidator,
    getJPWinLogsHandler);

router.put("/entities/:path/promo/freebet/:promoId/players/group",
    authenticate,
    authorize,
    decodePid(),
    validateBulkCreate,
    addFreebetsToPlayers);

router.put("/promo/freebet/:promoId/players/group",
    authenticate,
    authorize,
    decodePid(),
    validateBulkCreate,
    addFreebetsToPlayers);

router.put("/players/:playerCode/freebet/:promoId",
    authenticate,
    authorize,
    decodePid(),
    addFreebetsToPlayerFromPromo);

router.get("/players/:playerCode/promo",
    authenticate,
    authorize,
    getPlayerPromotions);

router.get("/players/:playerCode/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    getPlayerPromotion);

router.get("/players/:playerCode/promo/:promoId/freeBetLeft",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    getPlayerPromotionFreeBetLeft);

router.delete("/players/:playerCode/promo/:promoId",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    revokePromoFromPlayer);

router.get("/entities/:path/players/:playerCode/promo",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getPlayerPromotions);

router.get("/entities/:path/players/:playerCode/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    getPlayerPromotion);

router.get("/entities/:path/players/:playerCode/promo/:promoId/freeBetLeft",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    getPlayerPromotionFreeBetLeft);

router.delete("/entities/:path/players/:playerCode/promo/:promoId",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    revokePromoFromPlayer);

router.put("/entities/:path/players/:playerCode/freebet/:promoId",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    addFreebetsToPlayerFromPromo);

router.get("/promo/:promoId/players", authenticate, authorize, decodePid(), getPromotionPlayers);
router.get("/entities/:path/promo/:promoId/players", authenticate, authorize, decodePid(), getPromotionPlayers);
router.put("/promo/:promoId/players", authenticate, authorize, decodePid(), addPromotionToPlayers);
router.post("/promo/:promoId/players",
    authenticate,
    authorize,
    decodePid(),
    validatePlayersCodes,
    addPromotionToPlayersByPlayersCodes);
router.put("/entities/:path/promo/:promoId/players", authenticate, authorize, decodePid(), addPromotionToPlayers);
router.post("/entities/:path/promo/:promoId/players",
    authenticate,
    authorize,
    decodePid(),
    validatePlayersCodes,
    addPromotionToPlayersByPlayersCodes);

router.get("/players/:playerCode/code-is-used",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    checkPlayerCodeIsTaken);

router.get("/players/:email/mail-is-used",
    authenticate,
    authorize,
    validateMail,
    checkMailIsTaken);

router.post("/merchants/game/url",
    validatePlayMode,
    sanitize(req => {
        (req.sanitize("language") as any).toLowerCase();
    }),
    validateLanguage,
    setMerchantAuthContext,
    getMerchantGameUrl
);

router.post("/merchants/lobby/url",
    sanitize(req => {
        (req.sanitize("language") as any).toLowerCase();
    }),
    validate({
        merchantType: { notEmpty: true, isWord: true },
        merchantCode: { notEmpty: true, isWord: true },
        ticket: { notEmpty: true },
        lobbyId: { optional: true, isWord: true, isLength: { options: { min: 1, max: 255 } } },
        language: { optional: true, isLanguage: true }
    }),
    setMerchantAuthContext,
    decodePid({ forceReturnIfNumber: true }),
    getMerchantLobbyUrl
);

router.delete("/entities/:path/players/:playerCode/session",
    authenticate,
    authorize,
    auditable,
    killSession);

router.get("/entities/:path/history/unfinished/game",
    authenticate,
    authorize,
    validate({
        playerCode: { notEmpty: true, isMerchantPlayerCode: true }
    }),
    getUnfinishedRounds
);

router.post("/entities/:path/players/:playerCode/password/set-for-unpassworded", authenticate, authorize,
    validatePlayerCodeForOperator, validateSetPlayerPassword, setPlayerPasswordIfEmpty);

router.post("/entities", authenticate, authorize, validate(validateEntityCreation), createEntity);

router.post("/entities/:path", authenticate, authorize, validate(validateEntityCreation), createEntity);
router.patch("/entities/:path", authenticate, authorize, validate(validateEntityUpdate), updateEntity);
router.get("/entities/:path", authenticate, authorize, getEntityDetails);
router.delete("/entities/:path", authenticate, authorize, deleteEntity);

router.post("/brandentities", authenticate, authorize, validateBrand, createBrand);
router.post("/brandentities/:path", authenticate, authorize, validateBrand, createBrand);

// get info of key entity
router.get("/merchantentities", authenticate, authorize, getMerchantEntityInfo);
// create merchant entity under key entity
router.post("/merchantentities",
    authenticate,
    authorize,
    validateCreateMerchantEntityData,
    decodePid(),
    createMerchantEntity);
router.patch("/merchantentities",
    authenticate,
    authorize,
    validateUpdateMerchantEntityData,
    decodePid({ allowNull: true }),
    updateMerchantEntity
);

// find merchant entity by path
router.get("/merchantentities/:path", authenticate, authorize, getMerchantEntityInfo);
// create merchant entity under specific path
router.post("/merchantentities/:path",
    authenticate,
    authorize,
    validateCreateMerchantEntityData,
    decodePid(),
    createMerchantEntity
);
router.patch("/merchantentities/:path",
    authenticate,
    authorize,
    validateUpdateMerchantEntityData,
    decodePid({ allowNull: true }),
    updateMerchantEntity
);
router.delete("/merchantentities/:path", authenticate, authorize, deleteEntity);

router.get("/children/roles", authenticate, authorize, validateSearchByTitle, getChildrenRolesList);
router.get("/entities/:path/children/roles", authenticate, authorize, validateSearchByTitle, getChildrenRolesList);
router.get("/roles", authenticate, authorize, validateSearchByTitle, getRolesList);
router.get("/entities/:path/roles", authenticate, authorize, validateSearchByTitle, getRolesList);

router.get("/users", authenticate, authorize, getUserValidator, defineLimits, getUser);
router.post("/users", authenticate, authorize, CREATE_USER_VALIDATOR, createUser);
router.get("/users/:username", authenticate, authorize, validateUsername, getUserDetails);
router.patch("/users/:username", authenticate, authorize, CHANGE_USER_VALIDATOR, patchUserDetails);
router.delete("/users/:username", authenticate, authorize, deleteUserHandler);

router.get("/entities/:path/users", authenticate, authorize, defineLimits, getUserValidator, getUser);
router.post("/entities/:path/users", authenticate, authorize, CREATE_USER_VALIDATOR, createUserUnderPath);
router.get("/entities/:path/users/:username", authenticate, authorize, validateUsername, getUserDetails);
router.patch("/entities/:path/users/:username", authenticate, authorize, CHANGE_USER_VALIDATOR, patchUserUnderPath);
router.delete("/entities/:path/users/:username", authenticate, authorize, deleteUserHandler);

router.get("/structure", authenticate, authorize, sanitizeBoolean("includeProxy", "includeMerchantCode"), getStructure);
router.get("/short-structure", authenticate, authorize, getShortStructure);
router.post("/structure/move-entity", authenticate, authorize, validateMoveEntity, moveEntity);

router.get("/entities/:path/structure",
    authenticate,
    authorize,
    sanitizeBoolean("includeProxy", "includeMerchantCode"),
    getStructure);
router.get("/entities/:path/short-structure", authenticate, authorize, getShortStructure);

async function updateGameLimits(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const entityService = await getEntityGameService(entity);

        res.send(await entityService.updateLimits(req.params.gameCode, req.body));
    } catch (err) {
        return next(err);
    }
}

router.post("/entities/:path/games", authenticate, authorize, defineIsLiveGameFlagFalse, addGamesToEntity);
router.delete("/entities/:path/games",
    authenticate,
    authorize,
    auditable,
    defineIsLiveGameFlagFalse,
    removeGamesFromEntity);
router.patch("/entities/:path/games/:gameCode", authenticate, authorize, validateGameCode, updateGameLimits);
router.patch("/games/:gameCode", authenticate, authorize, validateGameCode, updateGameLimits);

router.post("/games/group/limits", authenticate, authorize, validateGroupLimitFiltersUpdate, updateGamesLimits);
router.post("/entities/:path/games/group/limits",
    authenticate, authorize, validateGroupLimitFiltersUpdate, updateGamesLimits);

router.post("/payments/direct/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    directTransfersOut);

router.post("/payments/direct/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    directTransfersIn);

/* PROMOTION API */

router.get("/entities/:path/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validateGetPromoSchema),
    getPromos);

router.post("/entities/:path/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validatePostPromoSchema),
    validatePromoPermissionsPerType(PROMO_OPERATIONS.CREATE),
    createPromo);

router.patch("/entities/:path/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validatePatchPromoSchema),
    validatePromoPermissionsPerType(PROMO_OPERATIONS.EDIT),
    patchPromo);

router.get("/entities/:path/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    getPromoById);

router.delete("/entities/:path/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    deletePromo);

router.get("/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validateGetPromoSchema),
    getPromos);

router.get("/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    getPromoById);

router.post("/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validatePostPromoSchema),
    validatePromoPermissionsPerType(PROMO_OPERATIONS.CREATE),
    createPromo);

router.patch("/promo",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["externalId"] }),
    validate(validatePatchPromoSchema),
    validatePromoPermissionsPerType(PROMO_OPERATIONS.EDIT),
    patchPromo);

router.delete("/promo/:promoId",
    authenticate,
    authorize,
    decodePid(),
    deletePromo);

router.patch(
    ["/player/change-nickname", "/entities/:path/player/change-nickname"],
    authenticate,
    authorize,
    changeNickname
);

export default router;

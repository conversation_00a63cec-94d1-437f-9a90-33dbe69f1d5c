import { NextFunction, Request, Response, Router } from "express";
import {
    auditable,
    authenticate,
    authorize,
    convertDatesToISOMiddleware,
    defineDatesRangeLimitsMiddleware,
    defineLimits,
    FormattedResponse,
    getEntity,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { BaseEntity } from "../entities/entity";
import * as PaymentService from "../services/payment";
import { DirectTransferData, OrderInfo, TransferData, } from "../entities/payment";
import { BrandEntity } from "../entities/brand";
import { parseFilter } from "../services/filter";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { CurrencyNotFoundError, DuplicateTransactionError } from "../errors";
import * as PlayService from "../services/playService";
import logger from "../utils/logger";
import { getEntityPlayerFinanceService } from "../services/payment";
import * as Errors from "../errors";
import { getEntitySettings } from "../services/settings";

const log = logger("payment");
const router: Router = Router();

export async function transfersIn(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const entitySettings = await getEntitySettings(entity.path);

        req.body.amount = normalizeCurrencyAmount(req.body.currency, req.body.amount);
        const data: TransferData = req.body;
        const transactionInfo: OrderInfo =
            await getEntityPlayerFinanceService().transferIn(
                entity as BrandEntity,
                data,
                !!entitySettings.throwErrorOnDuplicateTransaction
            );

        log.info({ requestBody: req.body, transactionInfo: transactionInfo }, "transfersIn");

        let responseStatus = 200;
        if (transactionInfo.isNew) {
            responseStatus = 201;
        } else if (entitySettings.throwErrorOnDuplicateTransaction) {
            return next(new DuplicateTransactionError(data.extTrxId));
        }
        delete transactionInfo.isNew;
        res.status(responseStatus).send(transactionInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function transfersOut(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const entitySettings = await getEntitySettings(entity.path);

        req.body.amount = normalizeCurrencyAmount(req.body.currency, req.body.amount);
        const data: TransferData = req.body;
        const transactionInfo: OrderInfo =
            await getEntityPlayerFinanceService().transferOut(
                entity as BrandEntity,
                data,
                !!entitySettings.throwErrorOnDuplicateTransaction
            );

        log.info({ requestBody: req.body, transactionInfo: transactionInfo }, "transfersOut");

        let responseStatus = 200;
        if (transactionInfo.isNew) {
            responseStatus = 201;
        } else if (entitySettings.throwErrorOnDuplicateTransaction) {
            return next(new DuplicateTransactionError(data.extTrxId));
        }
        delete transactionInfo.isNew;
        res.status(responseStatus).send(transactionInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function payments(req: Request & KeyEntityHolder, res: FormattedResponse, next: NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const transactionInfo: OrderInfo[] = await PaymentService.getPaymentsList(
            entity as BrandEntity,
            parseFilter(req.query, PaymentService.queryParamsKeys)
        );
        res.sendFormatted(req, transactionInfo, ["orderInfo"]);
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Return amount, rounded to currency multiplier. ie 1.234(USD) -> 1.23
 * Can throw CurrencyNotFoundError if currencyCode not exist in global currencies list
 * @param amount in major units (decimal)
 * @param currencyCode one of currency code from global currencies list
 * @returns {number} normalized(rounded to currency multiplier) amount (decimal)
 */
export function normalizeCurrencyAmount(currencyCode: string, amount: number): number {
    let currency: Currency;
    try {
        currency = Currencies.get(currencyCode);
    } catch (err) {
        throw new CurrencyNotFoundError(currencyCode);
    }
    const minorUnits = currency.toMinorUnits(amount);
    return currency.toMajorUnits(minorUnits);
}

export async function getTransactionInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = await getEntity(req);
        const response = await PlayService.getTransactionInfo(entity.id, req.params.transactionId, "transfer");
        res.send(response);
        next();
    } catch (err) {
        return next(err);
    }
}

export async function updateTransactionStatus(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = await getEntity(req);
        const response = await getEntityPlayerFinanceService().checkStatusAndGetPaymentOrder(entity.id,
            req.params.transactionId);
        if (!response) {
            return next(new Errors.PaymentNotFoundError());
        }
        res.send(response);
        next();
    } catch (err) {
        return next(err);
    }
}

export async function directTransfersOut(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const toEntity: BaseEntity = getEntity(req);
        const entitySettings = await getEntitySettings(toEntity.path);
        req.body.amount = normalizeCurrencyAmount(req.body.currency, req.body.amount);
        const data: DirectTransferData = req.body;
        const transactionInfo: OrderInfo =
            await getEntityPlayerFinanceService().transferOutFromPlayerToParent(toEntity, data);

        log.info({ requestBody: req.body, transactionInfo: transactionInfo }, "directTransfersOut");

        let responseStatus = 200;
        if (transactionInfo.isNew) {
            responseStatus = 201;
        } else if (entitySettings.throwErrorOnDuplicateTransaction) {
            return next(new DuplicateTransactionError(data.extTrxId));
        }
        delete transactionInfo.isNew;
        res.status(responseStatus).send(transactionInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function directTransfersIn(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const fromEntity: BaseEntity = getEntity(req);
        const entitySettings = await getEntitySettings(fromEntity.path);
        req.body.amount = normalizeCurrencyAmount(req.body.currency, req.body.amount);
        const data: DirectTransferData = req.body;
        const transactionInfo: OrderInfo =
            await getEntityPlayerFinanceService().transferInToPlayerFromParent(fromEntity, data);

        log.info({ requestBody: req.body, transactionInfo: transactionInfo }, "directTransfersIn");

        let responseStatus = 200;
        if (transactionInfo.isNew) {
            responseStatus = 201;
        } else if (entitySettings.throwErrorOnDuplicateTransaction) {
            return next(new DuplicateTransactionError(data.extTrxId));
        }
        delete transactionInfo.isNew;
        res.status(responseStatus).send(transactionInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateTransfers = validate({
    playerCode: { notEmpty: true },
    currency: { notEmpty: true },
    amount: { notEmpty: true, isDecimal: true, isPositive: true },
    extTrxId: { optional: true, isString: true }
});

export const validateGetPayments = validate({
    amount: { optional: true, isDecimal: true, isPositive: true },
    amount__gt: { optional: true, isDecimal: true, isPositive: true },
    amount__lt: { optional: true, isDecimal: true, isPositive: true },
    amount__gte: { optional: true, isDecimal: true, isPositive: true },
    amount__lte: { optional: true, isDecimal: true, isPositive: true },
    startDate: { optional: true, isTimestampIso8601: true },
    startDate__gt: { optional: true, isTimestampIso8601: true },
    startDate__gte: { optional: true, isTimestampIso8601: true },
    startDate__lt: { optional: true, isTimestampIso8601: true },
    startDate__lte: { optional: true, isTimestampIso8601: true },
    endDate: { optional: true, isTimestampIso8601: true },
    endDate__gt: { optional: true, isTimestampIso8601: true },
    endDate__gte: { optional: true, isTimestampIso8601: true },
    endDate__lt: { optional: true, isTimestampIso8601: true },
    endDate__lte: { optional: true, isTimestampIso8601: true }
});

export const validateTransactionId = validate({
    transactionId: { isString: true }
});

router.post("/payments/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    auditable,
    transfersIn);

router.post("/entities/:path/payments/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    auditable,
    transfersIn);

router.post("/payments/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    auditable,
    transfersOut);

router.post("/entities/:path/payments/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    auditable,
    transfersOut);

router.get("/payments",
    authenticate,
    authorize,
    defineLimits,
    convertDatesToISOMiddleware(["startDate", "endDate"]),
    validateGetPayments,
    defineDatesRangeLimitsMiddleware(["startDate"], false),
    payments);

router.get("/entities/:path/payments",
    authenticate,
    authorize,
    defineLimits,
    convertDatesToISOMiddleware(["startDate", "endDate"]),
    validateGetPayments,
    defineDatesRangeLimitsMiddleware(["startDate"], false),
    payments);

router.get("/payments/:transactionId",
    authenticate,
    authorize,
    getTransactionInfo);

router.get("/entities/:path/payments/:transactionId",
    authenticate,
    authorize,
    getTransactionInfo);

router.patch("/payments/:transactionId",
    validateTransactionId,
    authenticate,
    authorize,
    auditable,
    updateTransactionStatus);

router.patch("/entities/:path/payments/:transactionId",
    validateTransactionId,
    authenticate,
    authorize,
    auditable,
    updateTransactionStatus);

router.post("/payments/direct/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    auditable,
    directTransfersOut);

router.post("/payments/direct/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    auditable,
    directTransfersIn);

router.post("/entities/:path/payments/direct/transfers/out",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    auditable,
    directTransfersOut);

router.post("/entities/:path/payments/direct/transfers/in",
    authenticate,
    authorize,
    validateTransfers,
    validate({ brandPath: { notEmpty: true } }),
    auditable,
    directTransfersIn);

export default router;

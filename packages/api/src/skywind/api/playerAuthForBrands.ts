import { NextFunction, Request, Response, Router } from "express";
import * as PlayerLoginService from "../services/playerLogin";
import * as Errors from "../errors";
import { BrandEntity } from "../entities/brand";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntityPath,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { getPlayerLoginService } from "../services/player/playerLogin";
import { MAX_INT_VALUE, X_PLAYER_TOKEN } from "../utils/common";
import { findIp } from "../utils/requestHelper";
import { Entity } from "../entities/entity";
import EntityCache from "../cache/entity";
import { getPlayerInfoService } from "../services/playerInfo";

const router: Router = Router();

const validateCredentials = validate({
    code: { notEmpty: true, isWord: true },
    password: { notEmpty: true },
    force: { optional: true, isBoolean: true }
});

export const validatePlayerCode = validate({
    playerCode: { notEmpty: true, isPlayerCode: true },
});

export const validatePlayerCodeForOperator = validate({
    playerCode: { notEmpty: true, isPlayerCodeFromOperator: true }
});

export const validateBonusId = validate({
    bonusId: { notEmpty: true, isString: true }
});

export const validateCreatePlayerBonus = validate({
    type: { notEmpty: true, isString: true },
    title: { notEmpty: true, isString: true },
    description: { notEmpty: true, isString: true },
    amount: { notEmpty: true, isInt: true, isPositive: true },
    gameCode: {
        notEmpty: true,
        oneOf: [
            {
                isString: true,
            },
            {
                isArray: true,
                notEmpty: true
            }
        ]
    },
    coin: { notEmpty: true, isNumeric: true, isPositive: true },
    currency: { notEmpty: true, isCurrency: true },
    expiresAt: { notEmpty: true, isISO8601: true },
    externalId: { optional: true, isString: true }
});

export const validatePlayerUpdate = validate({
    playerCode: { notEmpty: true, isWord: true },
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: true },
    status: { isStatus: true, optional: true },
    isVip: { optional: true, isBoolean: true }
});

export const validatePlayerInfo = validate({
    playerCode: { notEmpty: true, isWord: true },
    isVip: { optional: true, isBoolean: true },
    nickname: { optional: true },
    isTracked: { optional: true, isBoolean: true },
    hasWarn: { optional: true, isBoolean: true },
    isPublicChatBlock: { optional: true, isBoolean: true },
    isPrivateChatBlock: { optional: true, isBoolean: true },
    restrictedIpCountries: { optional: true },
    "restrictedIpCountries.countries": { optional: true, isCountryCode: { options: { allowNull: true } } },
    "restrictedIpCountries.ignore": { optional: true, isBoolean: true },
});
export const validateResetPassword = validate({
    email: { notEmpty: true, isEmail: true },
    redirectTo: { optional: true },
});

export const validatePasswordConfirm = validate({
    password: { notEmpty: true },
    guid: { notEmpty: true },
});

export const validateUpdatePlayerPassword = validate({
    oldPassword: { optional: true, isPassword: true },
    newPassword: { notEmpty: true, isPassword: true },
    confirmPassword: { notEmpty: true, isPassword: true }
});

export const validateSetPlayerPassword = validate({
    newPassword: { notEmpty: true, isPassword: true },
    confirmPassword: { notEmpty: true, isPassword: true }
});

export const validateEmail = validate({
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: true }
});

export const validatePlayerInfoForStudioUser = validate({
    playerCode: { notEmpty: true },
    brandId: { notEmpty: true, isInt: { options: { min: 0, max: MAX_INT_VALUE } } },
    isVip: { optional: true, isBoolean: true },
    nickname: { optional: true },
    isTracked: { optional: true, isBoolean: true },
    hasWarn: { optional: true, isBoolean: true },
    isPublicChatBlock: { optional: true, isBoolean: true },
    isPrivateChatBlock: { optional: true, isBoolean: true },
});

router.delete("/players/info/:brandPID/:playerCode/change-nickname/attempts",
    authenticate,
    authorize,
    decodePid(),
    resetNicknameChangeAttempts);

async function resetNicknameChangeAttempts(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brandId = +req.params.brandPID;
        const userEntity = await EntityCache.findOne<Entity>({ id: brandId },
            undefined,
            true,
            true);
        if (!userEntity.isBrand()) {
            throw new Errors.NotBrand();
        }

        await getPlayerInfoService().resetNicknameChangeAttempts(req.params.playerCode, brandId);
        res.status(204).end();
    } catch (e) {
        next(e);
    }
}

router.post("/players/login", authenticate, authorize,
    validateCredentials, decodePid(), auditable, loginPlayer);
router.post("/entities/:path/players/login", authenticate, authorize,
    validateCredentials, decodePid(), auditable, loginPlayer);

router.put("/players/:playerCode/password", authenticate, authorize, validatePlayerCode,
    validateUpdatePlayerPassword, auditable, updatePlayerPassword);
router.put("/entities/:path/players/:playerCode/password", authenticate, authorize, validatePlayerCode,
    validateUpdatePlayerPassword, auditable, updatePlayerPassword);

router.post("/players/password/reset", authenticate, authorize,
    validateResetPassword, auditable, playerResetPassword);
router.post("/entities/:path/players/password/reset",
    authenticate, authorize, validateResetPassword, auditable, playerResetPassword);

router.post("/players/password/confirm", authenticate, authorize,
    validatePasswordConfirm, auditable, playerPasswordConfirm);
router.post("/entities/:path/players/password/confirm",
    authenticate, authorize, validatePasswordConfirm, auditable, playerPasswordConfirm);

router.get("/players/password/:playerCode/temporary", authenticate, authorize,
    validatePlayerCode, setTemporaryPassword);
router.get("/entities/:path/players/password/:playerCode/temporary",
    authenticate, authorize, validatePlayerCode, setTemporaryPassword);

router.post("/players/:playerCode/password/set-for-unpassworded", authenticate, authorize, validatePlayerCode,
    validateSetPlayerPassword, auditable, setPlayerPasswordIfEmpty);
router.post("/entities/:path/players/:playerCode/password/set-for-unpassworded", authenticate, authorize,
    validatePlayerCode, validateSetPlayerPassword, auditable, setPlayerPasswordIfEmpty);

async function getBrand(req: Request & KeyEntityHolder): Promise<BrandEntity> {
    const searchBy = req.params.path ? { path: getEntityPath(req) } : { id: req.params.brandId || req.body.brandId };
    const entity: BrandEntity = req.keyEntity.find(searchBy) as BrandEntity;
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    if (!entity.isBrand()) {
        throw new Errors.NotBrand();
    }
    if (entity.status === "suspended") {
        throw new Errors.ParentSuspendedError();
    }
    return entity;
}

async function loginPlayer(req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        const loginData: PlayerLoginService.LoginData = req.body;
        loginData.userId = req.userId;
        const playerAuthService = getPlayerLoginService(brand);
        const loginInfo: PlayerLoginService.LoginInfo = await playerAuthService.login(loginData, findIp(req) || req.ip);
        res.header(X_PLAYER_TOKEN, loginInfo.token);
        res.send(loginInfo);
        next();
    } catch (err) {
        next(err);
    }
}

async function playerResetPassword(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        res.status(204).send(await PlayerLoginService.resetPlayerPassword(brand, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function playerPasswordConfirm(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        res.status(204).send(await PlayerLoginService.playerPasswordConfirm(brand, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function setTemporaryPassword(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        res.send(await PlayerLoginService.setTemporaryPassword(brand, req.params.playerCode));
        next();
    } catch (err) {
        next(err);
    }
}

export async function updatePlayerPassword(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        const playerInfo = await PlayerLoginService.updatePlayerPassword(brand, req.params.playerCode, req.body);
        res.send(playerInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function checkMailIsTaken(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        await PlayerLoginService.checkPlayerMailIsTaken(brand, req.params.email);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

export async function checkPlayerCodeIsTaken(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        await PlayerLoginService.checkPlayerCodeIsTaken(brand, req.params.playerCode);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

export async function setPlayerPasswordIfEmpty(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await getBrand(req);
        const playerInfo = await PlayerLoginService.updatePlayerPassword(brand, req.params.playerCode, req.body, true);
        res.send(playerInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;

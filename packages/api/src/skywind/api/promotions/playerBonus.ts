import * as express from "express";
import { getBrand } from "../middleware/middleware";
import PlayerBonusService, { PlayerBonusType } from "../../services/promotions/playerBonusService";
import { KeyEntityHolder } from "../../services/security";
import { BaseEntity } from "../../entities/entity";
import { getEntitySettings } from "../../services/settings";
import { PlayerBonusesNotEnabledError } from "../../errors";

type Request = express.Request & KeyEntityHolder;

async function checkIfBonusesAreAllowed(entity: BaseEntity) {
    const entitySettings= await getEntitySettings(entity.path);
    if (!entitySettings.playerBonusesEnabled) {
        throw new PlayerBonusesNotEnabledError();
    }
}

export async function createPlayerBonus(
    req: Request,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getBrand(req);
        await checkIfBonusesAreAllowed(entity);
        const playerBonus = await PlayerBonusService.createPlayerBonus(entity, {
            playerCode: req.params.playerCode,
            ...req.body
        });
        res.send(playerBonus);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getPlayerBonuses(
    req: Request,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getBrand(req);
        await checkIfBonusesAreAllowed(entity);
        const playerBonuses = await PlayerBonusService.getPlayerBonuses(
            entity,
            req.params.playerCode,
            req.params.playerBonusType || PlayerBonusType.FreeBet
        );
        res.send(playerBonuses);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getPlayerBonus(
    req: Request,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getBrand(req);
        await checkIfBonusesAreAllowed(entity);
        const playerBonus = await PlayerBonusService.getPlayerBonus(
            entity.id,
            req.params.playerCode,
            req.query.playerBonusType || PlayerBonusType.FreeBet,
            req.params.bonusId
        );
        res.send(playerBonus);
        next();
    } catch (err) {
        next(err);
    }
}

export async function removePlayerBonus(
    req: Request,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getBrand(req);
        await checkIfBonusesAreAllowed(entity);
        await PlayerBonusService.removePlayerBonus(
            entity,
            req.params.playerCode,
            req.query.playerBonusType || PlayerBonusType.FreeBet,
            req.params.bonusId,
            req.query.currency
        );
        res.sendStatus(204);
        next();
    } catch (err) {
        next(err);
    }
}

export async function removePlayerBonuses(
    req: Request,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getBrand(req);
        await checkIfBonusesAreAllowed(entity);
        await PlayerBonusService.removePlayerBonuses(
            entity,
            req.params.playerCode,
            req.query.playerBonusType || PlayerBonusType.FreeBet,
            req.query.currency
        );
        res.sendStatus(204);
        next();
    } catch (err) {
        next(err);
    }
}

import * as express from "express";
import {
    authenticate, authorize, decodePid, getBooleanParamFromRequestQuery, getBrand,
    validate
} from "../middleware/middleware";
import { KeyEntityHolder } from "../../services/security";
import { PlayerPromotionService } from "../../services/promotions/playerPromotionService";
import { parseFilter } from "../../services/filter";
import { PlayerPromotionDbImpl } from "../../services/promotions/playerPromotionDb";
import {
    validateBonusId,
    validateCreatePlayerBonus,
    validatePlayerCodeForOperator
} from "../playerAuthForBrands";
import {
    createPlayerBonus,
    getPlayerBonus,
    getPlayerBonuses,
    removePlayerBonus,
    removePlayerBonuses
} from "./playerBonus";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

export async function getPlayerPromotions(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.getPlayerPromotions(entity, req.params.playerCode, req.query.type);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getPlayerPromotion(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.getPlayerPromotion(entity,
            req.params.playerCode,
            req.params.promoId);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getPlayerPromotionFreeBetLeft(req: Request, res: express.Response,
                                                    next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const freeBetLeft = await PlayerPromotionService.getFreeBetLeft(entity,
            req.params.playerCode,
            req.params.promoId);
        res.send(freeBetLeft);
        next();
    } catch (err) {
        next(err);
    }
}

async function addPromoToPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.addPromotionToPlayer(entity,
            req.params.promoId,
            req.params.playerCode,
            req.body);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function revokePromoFromPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        await PlayerPromotionService.removePlayerFromPromotion(entity,
            req.params.promoId,
            req.params.playerCode,
            getBooleanParamFromRequestQuery(req, "force"));
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

const validatePlayerPromoUpdateRequest = validate({
    promoId: { notEmpty: true },
    amount: { optional: true, isInt: { options: { strict: true } } },
    expirationPeriod: { optional: true, isInt: { options: { strict: true } } },
    expirationPeriodType: { optional: true, isPromoInterval: { errorMessage: "Invalid reward time period" } }
});
const MAX_PLAYERS_CODES = 1000;
export const validatePlayersCodes = validate({
    playersCodes: {
        isArray: { options: { notEmpty: true }, errorMessage: "should not be empty" },
        isMaxLength: {
            options: { max: MAX_PLAYERS_CODES },
            errorMessage: `max players codes: ${MAX_PLAYERS_CODES}`
        }
    },
});

async function updatePromoForPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const balance = await PlayerPromotionService.updatePlayerPromotion(entity,
            req.params.playerCode,
            req.params.promoId,
            req.body);
        res.send(balance);
        next();
    } catch (err) {
        next(err);
    }
}

export async function addPromotionToPlayersByPlayersCodes(req: Request,
                                                          res: express.Response,
                                                          next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.addPromotionToPlayers(
            entity,
            req.params.promoId,
            req.body.playersCodes);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function addPromotionToPlayers(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const filters = Object.keys(req.query).length ? req.query : req.body;
        const promo = await PlayerPromotionService.addPromotionToPlayersByFilter(
            entity,
            req.params.promoId,
            filters);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getPromotionPlayers(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.getPromotionPlayers(
            entity,
            req.params.promoId,
            parseFilter(req.query, PlayerPromotionDbImpl.queryParamsKeys),
            parseFilter(req.query, PlayerPromotionDbImpl.limitQueryParamsKeys));
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/players/:playerCode/promo", authenticate, authorize, getPlayerPromotions);
router.get("/entities/:path/players/:playerCode/promo", authenticate, authorize,
    getPlayerPromotions);
router.get("/players/:playerCode/promo/:promoId", authenticate, authorize,
    decodePid(), getPlayerPromotion);
router.get("/entities/:path/players/:playerCode/promo/:promoId", authenticate, authorize,
    decodePid(), getPlayerPromotion);
router.put("/players/:playerCode/promo/:promoId", authenticate, authorize,
    validatePlayerPromoUpdateRequest, decodePid(), addPromoToPlayer);
router.put("/entities/:path/players/:playerCode/promo/:promoId",
    authenticate, authorize, validatePlayerPromoUpdateRequest, decodePid(), addPromoToPlayer);
router.patch("/players/:playerCode/promo/:promoId", authenticate, authorize,
    validatePlayerPromoUpdateRequest, decodePid(), updatePromoForPlayer);
router.patch("/entities/:path/players/:playerCode/promo/:promoId",
    authenticate, authorize, validatePlayerPromoUpdateRequest, decodePid(), updatePromoForPlayer);
router.delete("/players/:playerCode/promo/:promoId",
    authenticate, authorize, decodePid(), revokePromoFromPlayer);
router.delete("/entities/:path/players/:playerCode/promo/:promoId",
    authenticate, authorize, decodePid(), revokePromoFromPlayer);

router.get("/promo/:promoId/players", authenticate, authorize, decodePid(), getPromotionPlayers);
router.get("/entities/:path/promo/:promoId/players", authenticate, authorize, decodePid(), getPromotionPlayers);
router.put("/promo/:promoId/players", authenticate, authorize, decodePid(), addPromotionToPlayers);
router.put("/entities/:path/promo/:promoId/players", authenticate, authorize, decodePid(), addPromotionToPlayers);
router.post("/promo/:promoId/players",
    authenticate,
    authorize,
    decodePid(),
    validatePlayersCodes,
    addPromotionToPlayersByPlayersCodes);
router.post("/entities/:path/promo/:promoId/players",
    authenticate,
    authorize,
    decodePid(),
    validatePlayersCodes,
    addPromotionToPlayersByPlayersCodes);
router.get("/players/:playerCode/promo/:promoId/freeBetLeft",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    getPlayerPromotionFreeBetLeft);
router.get("/entities/:path/players/:playerCode/promo/:promoId/freeBetLeft",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    decodePid(),
    getPlayerPromotionFreeBetLeft);

router.post(["/entities/:path/players/:playerCode/bonuses", "/players/:playerCode/bonuses"],
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateCreatePlayerBonus,
    createPlayerBonus
);
router.get(["/entities/:path/players/:playerCode/bonuses", "/players/:playerCode/bonuses"],
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getPlayerBonuses
);
router.delete(["/entities/:path/players/:playerCode/bonuses", "/players/:playerCode/bonuses"],
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    removePlayerBonuses
)
router.get(["/entities/:path/players/:playerCode/bonuses/:bonusId", "/players/:playerCode/bonuses/:bonusId"],
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateBonusId,
    getPlayerBonus
);
router.delete(["/entities/:path/players/:playerCode/bonuses/:bonusId", "/players/:playerCode/bonuses/:bonusId"],
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateBonusId,
    removePlayerBonus
)

export default router;

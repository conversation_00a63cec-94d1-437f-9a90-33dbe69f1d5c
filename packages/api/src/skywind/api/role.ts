import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    FormattedResponse,
    getBooleanParamFromRequestQuery,
    getEntity,
    validate
} from "./middleware/middleware";
import * as RoleService from "../services/role";
import { Entity } from "../entities/entity";
import { CreateData, Role, RoleInfo, UpdateData } from "../entities/role";
import { extractPermissions, KeyEntityHolder, PermissionsHolder, SwaggerHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import * as Errors from "../errors";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

async function createRole(req: Request, res: Response, next: NextFunction) {
    try {
        const data: CreateData = req.body;
        const entity: Entity = await getEntity(req) as Entity;
        data.entityId = entity.id;
        const newRole: Role = await RoleService.createRole(data);
        res.status(201).send(newRole.toDetailedInfo());
    } catch (err) {
        next(err);
    }
}

async function removeRecord(req: Request, res: Response, next: NextFunction) {
    try {
        const entity: Entity = await getEntity(req) as Entity;
        const countRemoved: number = await RoleService.removeRole(
            req.params.roleId,
            entity,
            getBooleanParamFromRequestQuery(req, "force"));
        if (countRemoved > 0) {
            res.status(204).end();
        } else {
            next(new Errors.RoleNotExist());
        }
    } catch (err) {
        next(err);
    }
}

export async function getRolesList(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const entity: Entity = await getEntity(req) as Entity;
        const roles: Array<RoleInfo> = await RoleService.listRoles(
            entity, parseFilter(req.query, RoleService.queryParamsKeys) || {});
        res.sendFormatted(req, roles);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getChildrenRolesList(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const entity: Entity = await getEntity(req) as Entity;
        const roles: Array<RoleInfo> = await RoleService.listChildrenRoles(
            entity, parseFilter(req.query, RoleService.queryParamsKeys) || {});
        res.sendFormatted(req, roles);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateRole(req: Request, res: Response, next: NextFunction) {
    try {
        const entity: Entity = await getEntity(req) as Entity;
        const data: UpdateData = req.body;
        const updatedItem: Role = await RoleService.updateRole(req.params.roleId, entity, data);
        res.send(updatedItem.toDetailedInfo());
    } catch (err) {
        next(err);
    }
}

async function getRole(req: Request, res: Response, next: NextFunction) {
    try {
        const entity: Entity = await getEntity(req) as Entity;
        const updatedItem: Role = await RoleService.getRoleFromEntity(req.params.roleId, entity);
        res.send(updatedItem.toDetailedInfo());
    } catch (err) {
        next(err);
    }
}

export const validateSearchByTitle = validate(prepareScheme(["title"]));
const validateRoleCreate = validate({
    title: {
        notEmpty: true,
    },
    permissions: {
        notEmpty: true,
    },
    isShared: {
        notEmpty: true,
        isBoolean: true,
    },
});
const validateRoleUpdate = validate({
    title: {
        notEmpty: true,
    },
    permissions: {
        notEmpty: true,
    },
});
const validateRemove = validate({
    force: {
        optional: true,
        isBoolean: true,
    },
});

export function validateRoleRelocationPermissions(holder: SwaggerHolder & PermissionsHolder): boolean {
    const operationPermissions = extractPermissions(holder);
    if (!operationPermissions) {
        return false;
    }
    const permissions = holder.permissions.grantedPermissions;
    return permissions.indexOf("role:move") >= 0 || permissions.indexOf("keyentity:role:move") >= 0;
}

function authorizeRoleRelocation(req: Request & SwaggerHolder & PermissionsHolder, res: Response, next: NextFunction) {
    if (validateRoleRelocationPermissions(req)) {
        next();
    } else {
        delete req.body.pathTo;
        next();
    }
}

router.post("/roles", authenticate, authorize,
    validateRoleCreate, auditable, createRole);
router.post("/entities/:path/roles", authenticate, authorize,
    validateRoleCreate, auditable, createRole);

router.patch("/roles/:roleId",
    authenticate,
    authorize,
    authorizeRoleRelocation,
    validateRoleUpdate,
    decodePid(),
    auditable,
    updateRole);
router.patch("/entities/:path/roles/:roleId",
    authenticate,
    authorize,
    authorizeRoleRelocation,
    validateRoleUpdate,
    decodePid(),
    auditable,
    updateRole);

router.get("/roles/:roleId", authenticate, authorize, decodePid(), getRole);
router.get("/entities/:path/roles/:roleId", authenticate, authorize, decodePid(), getRole);
router.delete("/roles/:roleId", authenticate, authorize,
    validateRemove, decodePid(), auditable, removeRecord);
router.delete("/entities/:path/roles/:roleId", authenticate, authorize,
    validateRemove, decodePid(), auditable, removeRecord);
router.get("/roles", authenticate, authorize, validateSearchByTitle, getRolesList);
router.get("/entities/:path/roles", authenticate, authorize, validateSearchByTitle, getRolesList);
router.get("/children/roles", authenticate, authorize, validateSearchByTitle, getChildrenRolesList);
router.get("/entities/:path/children/roles", authenticate, authorize, validateSearchByTitle, getChildrenRolesList);

export default router;

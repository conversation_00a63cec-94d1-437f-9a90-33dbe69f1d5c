import { Application } from "express";
import {
    apiJpnSwagger,
    apiSwagger,
    apiSwaggerPlayer,
    apiSwaggerSite,
    apiSwaggerV2,
} from "../utils/swagger";

import login from "./login";
import entity from "./entity";
import game from "./entityGame";
import liveGame from "./entityLiveGame";
import gameprovider from "./gameprovider";
import permissions from "./permissions";
import phantom from "./phantom";
import keyEntity from "./keyentity";
import gamegroup from "./gamegroup";
import gameRtp from "./gameRTP";
import settings from "./settings";
import health from "./expressRouters/health";
import id from "./id";
import merchant from "./merchant";
import agent from "./agent";
import siteToken from "./siteToken";
import gsSettings from "./gameServerSettings";
import role from "./role";
import playerAuthForBrands from "./playerAuthForBrands";
import playerAPI from "./playerAPI";
import brand from "./brand";
import language from "./language";
import currency from "./currency";
import country from "./country";
import report from "./report";
import biReport from "./biReports/biReports";
import biReportDomains from "./biReports/biReportDomains";
import history from "./history";
import historyPromotion from "./historyPromotion";
import reportJackpot from "./reportJackpot";
import reportJackpotV2 from "./v2/reportJackpot";
import version from "./expressRouters/version";
import payments from "./payment";
import audit from "./audit";
import auditSession from "./auditSession";
import notification from "./notification";
import label from "./label";
import gamecategory from "./gamecategory";
import availableSite from "./availableSites";
import lobby from "./lobby";
import terminal from "./terminal";
import promotion from "./promotions/promotion";
import blockedPlayer from "./blockedPlayer";
import merchantTestPlayer from "./merchantTestPlayer";
import entityPaymentHistory from "./entityPaymentHistory";
import jurisdiction from "./jurisdiction";
import playerPromotion from "./promotions/playerPromotion";
import playerFreebetPromotion from "./promotions/playerFreebetPromotion";
import playerBonusCoinPromotion from "./promotions/playerBonusCoinPromotion";
import domain from "./domain";
import staticDomainPool from "./staticDomainPool";
import dynamicDomainPool from "./dynamicDomainPool";
import site from "./site";
import playerSession from "./playerSession";
import historyV2 from "./v2/history";
import responsibleGamingMAPI from "./responsibleGamingMAPI";
import proxy from "./proxy";
import email from "./email";
import merchantType from "./merchantType";
import schemaDefinition from "./schemaDefinition";
import gameLimitsConfiguration from "./gameLimitsConfiguration";
import schemaConfiguration from "./schemaConfiguration";
import deploymentGroup from "./deploymentGroup";
import limitTemplate from "./limitTemplate";
import authGateway from "./authGateway";
import merchantPlayerGameGroup from "./merchantPlayerGameGroup";
import tokenService from "./blocked/token";
import stakeRange from "./stakeRange";
import integrationTest from "./integrationTest";
import limitLevels from "./limitLevels";
import entityLimitLevel from "./entityLimitLevel";
import gitbook from "./gitbook";
import flatReports from "./flatReports";
import entityJackpotConfigurationReport from "./entityJackpotConfigurationReport";
import oauth from "./oAuth";
import logger from "../utils/logger";
import { auditOnEnd, auditOnSend, encodePublicId, paging } from "./middleware/middleware";
import validator from "./middleware/validatorMiddleware";
import {
    addHeaderCacheControl,
    addHeaderCORS,
    createRequestLogger,
    resolveIp,
    sanitizeQueryParams,
    setUserAuthContext,
    validateContentType
} from "./middleware/baseMiddleware";
import config from "../config";
import gameLimitsCurrencies from "./gameLimitsCurrencies";
import jackpot from "./jackpot";
import { defineSwaggerExpress } from "./expressRouters/swagger";
import { createErrorHandler } from "./expressRouters/general";

const log = logger("routers");

export async function defineRoutes(app: Application): Promise<Application> {
    app.use(resolveIp);
    app.use(setUserAuthContext);
    app.use(validator);
    app.use(paging);
    if (config.audit.on) {
        // for audit middlewares order is important
        app.use(auditOnSend);
        app.use(auditOnEnd);
    }
    app.use(encodePublicId);

    await defineSwaggerExpress(app, await apiSwagger());

    await defineV1(app);
    await defineV2(app);
    await defineSiteAPI(app);
    await definePlayerAPI(app);
    await defineJPNApi(app);

    app.use(createErrorHandler(log));

    // respond with status OK when a GET request is made to the root directory
    app.route("/").get((req, res, next) => {
        res.status(200).end();
    });

    return app;
}

async function defineV1(app: Application) {
    app.use("/v1/*", validateContentType);
    app.use("/v1/*", sanitizeQueryParams);
    app.use("/v1/*", createRequestLogger(log));
    app.use("/v1/*", addHeaderCORS);
    app.use("/v1/*", addHeaderCacheControl);

    app.use("/v1/health", health);
    app.use("/v1/version", version);

    app.use("/v1/entities", entity);
    app.use("/v1/", game);
    app.use("/v1/", liveGame);
    app.use("/v1", permissions);
    app.use("/v1", phantom);
    app.use("/v1", keyEntity);
    app.use("/v1", gamegroup);
    app.use("/v1", gameRtp);
    app.use("/v1", settings);
    app.use("/v1", id);
    app.use("/v1", merchant);
    app.use("/v1", agent);
    app.use("/v1", siteToken);
    app.use("/v1", playerAuthForBrands);
    app.use("/v1", brand);
    app.use("/v1", report);
    app.use("/v1", biReport);
    app.use("/v1", biReportDomains);
    app.use("/v1", history);
    app.use("/v1", historyPromotion);
    app.use("/v1", reportJackpot);
    app.use("/v1", language);
    app.use("/v1", currency);
    app.use("/v1", country);
    app.use("/v1", audit);
    app.use("/v1", auditSession);
    app.use("/v1", notification);
    app.use("/v1", role);
    app.use("/v1", payments);
    app.use("/v1", label);
    app.use("/v1", gamecategory);
    app.use("/v1", availableSite);
    app.use("/v1", lobby);
    app.use("/v1", terminal);
    app.use("/v1", gameprovider);
    app.use("/v1", promotion);
    app.use("/v1", playerPromotion);
    app.use("/v1", playerFreebetPromotion);
    app.use("/v1", playerBonusCoinPromotion);
    app.use("/v1", jurisdiction);
    app.use("/v1", blockedPlayer);
    app.use("/v1", merchantTestPlayer);
    app.use("/v1", entityPaymentHistory);
    app.use("/v1", domain);
    app.use("/v1", staticDomainPool);
    app.use("/v1", dynamicDomainPool);
    app.use("/v1", playerSession);
    app.use("/v1", gsSettings);
    app.use("/v1", login);
    app.use("/v1", responsibleGamingMAPI);
    app.use("/v1", proxy);
    app.use("/v1", email);
    app.use("/v1", merchantType);
    app.use("/v1", schemaDefinition);
    app.use("/v1", gameLimitsConfiguration);
    app.use("/v1", schemaConfiguration);
    app.use("/v1", deploymentGroup);
    app.use("/v1", limitTemplate);
    app.use("/v1", merchantPlayerGameGroup);
    app.use("/v1", tokenService);
    app.use("/v1", stakeRange);
    app.use("/v1", integrationTest);
    app.use("/v1", limitLevels);
    app.use("/v1", entityLimitLevel);
    app.use("/v1", gitbook);
    app.use("/v1", flatReports);
    app.use("/v1", gameLimitsCurrencies);
    app.use("/v1", entityJackpotConfigurationReport);
    app.use("/v1", jackpot);
    app.use("/v1", oauth);
}

async function defineV2(app: Application) {
    app.use("/v2/*", sanitizeQueryParams);
    app.use("/v2/*", createRequestLogger(log));
    app.use("/v2/*", addHeaderCORS);
    app.use("/v2/*", addHeaderCacheControl);

    await defineSwaggerExpress(app, await apiSwaggerV2(), "v2");

    app.use("/v2", historyV2);
    app.use("/v2", reportJackpotV2);
}

async function defineSiteAPI(app: Application) {
    app.use("/site/v1/*", addHeaderCacheControl);
    const schema = await apiSwaggerSite();
    schema.basePath = "/site/v1";
    await defineSwaggerExpress(app, await apiSwaggerSite(), "site");

    app.use("/site/v1", site);
}

async function definePlayerAPI(app: Application) {
    app.use("/player/v1/*", addHeaderCacheControl);

    const schema = await apiSwaggerPlayer();
    schema.basePath = "/player/v1";
    await defineSwaggerExpress(app, schema, "player");

    app.use("/player/v1", playerAPI);
}

export async function defineJPNApi(app: Application) {
    app.use("/auth-gateway", authGateway);

    if (!config.jackpotServer.enableJPNSwagger) {
        return;
    }

    const swagger = await apiJpnSwagger();
    await defineSwaggerExpress(app, swagger, "jpn");
}

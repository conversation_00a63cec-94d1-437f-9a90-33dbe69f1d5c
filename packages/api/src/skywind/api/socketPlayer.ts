import type { Socket } from "socket.io";
import { type PlayerTokenInfo, validateToken } from "../services/playerSecurity";
import { logging } from "@skywind-group/sw-utils";
import { handleError } from "./middleware/errorMiddleware";
import { encodeEachInObject } from "../utils/publicid";
import { measureProvider } from "../utils/measures";
import { toPlayerInfo } from "../services/playerAPIService";
import { ContextVariables } from "../utils/contextVariables";

export default function socketPlayer(socket: Socket & { tokenData?: PlayerTokenInfo }, log: logging.Logger): void {
    socket.on("get-player-info", async (params?: { token?: string }): Promise<void> => {
        return measureProvider.runInTransaction("get-player-info", async () => {
            log.info({
                socketId: socket.id,
                rooms: socket.rooms,
                event: "get-player-info",
                data: params
            }, "Socket message received");
            try {
                const tokenData = await getTokenData(socket, params?.token);
                const playerInfo = await toPlayerInfo({
                    ...tokenData,
                    tokenData
                }, log);
                socket.emit("player-info", encodeEachInObject(playerInfo));
            } catch (error) {
                emitError(socket, error, log);
            }
        });
    });
}

export function socketRoom(entityId: number, playerCode: string) {
    return `${entityId}:${playerCode}`;
}

async function getTokenData(socket: Socket & { tokenData?: PlayerTokenInfo }, token?: string) {
    if (!socket.tokenData) {
        socket.tokenData = await validateToken(token || socket.handshake?.query?.["sw_player_token"] as string);
        await socket.join(socketRoom(socket.tokenData.brandId, socket.tokenData.playerCode));
        ContextVariables.setPlayerAuthData(socket.tokenData);
    }
    return socket.tokenData;
}

function emitError(socket: Socket, err: any, log: logging.Logger) {
    measureProvider.saveError(err);
    const error = handleError(err, log);
    const errorData = {
        code: error?.code,
        message: error?.message,
        extraData: {
            ...error?.extraData,
            traceId: measureProvider.getTraceID()
        },
    };
    log.error("Socket error emitted", {
        socketId: socket.id,
        rooms: socket.rooms,
        errorData,
        err
    });
    socket.emit("player-error", errorData);
}

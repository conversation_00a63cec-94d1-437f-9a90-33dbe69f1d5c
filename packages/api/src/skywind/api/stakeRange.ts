import { NextFunction, Request, Response, Router } from "express";
import { auditable, authenticate, authorize, validate } from "./middleware/middleware";
import StakeRangesService from "../services/stakeRanges";

const router: Router = Router();

async function getStakeRanges(req: Request, res: Response, next: NextFunction) {
    try {
        const stakeRanges = await StakeRangesService.findAll();
        res.send(stakeRanges);
    } catch (err) {
        next(err);
    }
}

async function updateStakeRanges(req: Request, res: Response, next: NextFunction) {
    try {
        const currency = req.body.currency;
        const coinBets = [...new Set(req.body.coinBets.sort((a, b) => a - b))] as number[];

        const lowerStakes = Array.isArray(req.body.lowerStakes) && req.body.lowerStakes.length ?
                            [...new Set(req.body.lowerStakes.sort((a, b) => a - b))] as number[] :
                            undefined;

        const updatedStake = await StakeRangesService.update(currency, coinBets, lowerStakes);
        res.send(updatedStake.toInfo());
    } catch (err) {
        next(err);
    }
}

async function createStakeRanges(req: Request, res: Response, next: NextFunction) {
    try {
        const currency = req.body.currency;
        const coinBets = [...new Set(req.body.coinBets.sort((a, b) => a - b))] as number[];
        const lowerStakes = Array.isArray(req.body.lowerStakes) && req.body.lowerStakes.length ?
                            [...new Set(req.body.lowerStakes.sort((a, b) => a - b))] as number[] :
                            null;

        const stakeRanges = await StakeRangesService.findOne(currency);
        if (stakeRanges) {
            return res.status(200).send(stakeRanges.toInfo());
        }

        const createdStakeRange = await StakeRangesService.create(currency, coinBets, lowerStakes);
        return res.status(201).send(createdStakeRange.toInfo());
    } catch (err) {
        next(err);
    }
}

async function dropStakeRanges(req: Request, res: Response, next: NextFunction) {
    try {
        await StakeRangesService.destroy(req.params.currency);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

const validationSchema = validate({
    currency: {
        isCurrency: true,
        notEmpty: true
    },
    coinBets: {
        isNumericArray: { options: { notEmpty: true } },
        notEmpty: true
    },
    lowerStakes: {
        optional: true,
        isNumericArray: true,
    }
});

router.get("/stake-ranges", authenticate, authorize, getStakeRanges);
router.patch("/stake-ranges", authenticate, authorize, auditable, validationSchema, updateStakeRanges);
router.post("/stake-ranges", authenticate, authorize, auditable, validationSchema, createStakeRanges);
router.delete("/stake-ranges/:currency", authenticate, authorize, auditable, dropStakeRanges);

export default router;

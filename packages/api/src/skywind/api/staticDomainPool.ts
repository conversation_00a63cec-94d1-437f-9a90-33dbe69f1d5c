import { auditable, authenticate, authorize, decodePid, getEntity, validate, getBooleanParamFromRequestQuery } from "./middleware/middleware";
import * as express from "express";
import { getStaticDomainPoolService } from "../services/staticDomainPool";
import { KeyEntityHolder } from "../services/security";
import { EntityStaticDomainPoolService } from "../services/entityStaticDomainPool";

const router: express.Router = express.Router();

async function getStaticDomainPools(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domains = await getStaticDomainPoolService().findAll();
        res.send(domains);
    } catch (err) {
        next(err);
    }
}

async function createStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainPoolService().create(req.body);
        res.status(201).send(domain);
    } catch (err) {
        next(err);
    }
}

async function getStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainPoolService().findById(req.params.poolId);
        res.send(domain);
    } catch (err) {
        next(err);
    }
}

async function updateStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainPoolService().update(req.params.poolId, req.body);
        res.send(domain);
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getStaticDomainPoolService().remove(req.params.poolId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function addStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        const result = await getStaticDomainPoolService().addDomain(poolId, domainId);
        res.send(result);
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().removeDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function enableStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().enableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function disableStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().disableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const inherited = getBooleanParamFromRequestQuery(req, "inherited", false);
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        const domain = await entityStaticDomainPoolService.getPool(inherited);
        res.send(domain);
    } catch (err) {
        next(err);
    }
}

async function addEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        const domain = await entityStaticDomainPoolService.addPool(req.params.poolId);
        res.send(domain);
    } catch (err) {
        next(err);
    }
}

async function removeEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        await entityStaticDomainPoolService.removePool();
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

// static domain pool management
router.get("/domain-pools/static", authenticate, authorize, getStaticDomainPools);
router.post("/domain-pools/static",
    authenticate,
    authorize,
    validate({
        name: { isString: true, notEmpty: true },
        domains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains"] }),
    auditable,
    createStaticDomainPool);
router.get("/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getStaticDomainPool);
router.patch("/domain-pools/:poolId/static",
    authenticate, authorize,
    validate({
        name: { isString: true, optional: true },
        domains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains"] }),
    auditable,
    updateStaticDomainPool);
router.delete("/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeStaticDomainPool);

router.put("/domain-pools/:poolId/static/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    addStaticDomainPoolItem);
router.delete("/domain-pools/:poolId/static/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/:domainId/enable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    enableStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/:domainId/disable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    disableStaticDomainPoolItem);



router.get("/entities/:path/domain-pools/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getEntityStaticDomainPool);
router.put("/entities/:path/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    addEntityStaticDomainPool);
router.delete("/entities/:path/domain-pools/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    removeEntityStaticDomainPool);

export default router;

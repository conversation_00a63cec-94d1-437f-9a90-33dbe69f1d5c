import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import {
    auditable,
    decodePid,
    getBooleanParamFromQuery,
    parseCommaSeparatedString,
    validate
} from "./middleware/middleware";
import * as TerminalService from "../services/terminal";
import { generate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, KeyEntityHolder } from "../services/security";
import { prepareScheme } from "../services/filter";
import { getFilteredGames } from "./site";
import config from "../config";
import { getMerchantService } from "../services/merchant";
import { validateGameCode } from "./entityGame";
import { VARCHAR_DEFAULT_LENGTH, X_TERMINAL_TOKEN } from "../utils/common";
import { LiveManagerService } from "../services/live";
import { GameCategoryGamesService } from "../services/gameCategory/gameCategoryGamesService";
import { GameCategoryService } from "../services/gameCategory/gameCategoryService";
import * as UrlManager from "../services/urlManager";
import { buildDynamicLiveManagerUrl } from "../services/entityDynamicDomainService";
import { getTerminalPlayerLoginService } from "../services/player/playerLogin";
import * as Errors from "../errors";
import { verifyTerminalToken } from "../utils/token";
import { findLobbyId, findLobbyImage, findLobbyOptions, findLobbyTheme, SHORT_INFO_FIELDS } from "../services/lobby";
import { pick } from "lodash";
import * as LobbyCache from "../cache/lobby";
import { decodeId, encodeId } from "../utils/publicid";
import { getEntitySettings } from "../services/settings";
import { getEntity } from "../services/playerSecurity";

const router: Router = Router();

interface LobbyIdHolder {
    lobbyId: number;
}

type Request = ExpressRequest & KeyEntityHolder & IpHolder;

export const validateCreateData = validate({
    status: { optional: true, isStatus: true },
    title: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
});

const validateFilter = validate(prepareScheme(["limit", "offset", "sortOrder", "currency"]));

async function authenticate(req: Request, _res: Response, next: NextFunction) {
    const token = req.header(X_TERMINAL_TOKEN);
    if (!token) {
        return next(new Errors.TerminalTokenIsMissing());
    }
    try {
        const { brandId } = await verifyTerminalToken(token);
        req.keyEntity = await getEntity(brandId);
        next();
    } catch (err) {
        return next(err);
    }
}

async function parseLobbyId(req: Request & LobbyIdHolder, _res: Response, next: NextFunction) {
    try {
        const token = req.header("x-terminal-token");
        const { lobbyId } = req.params;
        if (token) {
            const { brandId } = await verifyTerminalToken(token);
            req.keyEntity = await getEntity(brandId);
            req.lobbyId = decodeId(lobbyId);
        } else if (lobbyId) {
            const { id, brandId } = await findLobbyId(lobbyId);
            if (!id) {
                return next(new Errors.LobbyNotFound());
            }
            if (!brandId) {
                return next(new Errors.EntityCouldNotBeFound());
            }
            req.keyEntity = await getEntity(brandId);
            req.lobbyId = id;
        }
        if (!req.lobbyId) {
            return next(new Errors.LobbyNotFound());
        }
        next();
    } catch (err) {
        next(err);
    }
}

router.post("/terminals/players/login",
    authenticate,
    decodePid(),
    validate({
        code: { notEmpty: true, isWord: true },
        password: { notEmpty: true },
        force: { optional: true, isBoolean: true }
    }),
    auditable,
    async function ({ body, keyEntity, resolvedIp }: Request, res: Response, next: NextFunction) {
        const service = getTerminalPlayerLoginService(keyEntity, config.playerLoginToken);
        try {
            res.send(await service.login(body, resolvedIp));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/terminals/players/external/login",
    authenticate,
    // need to consider if this decodePid is needed
    decodePid({ ignoredKeys: ["operatorId", "gameId", "playerId"] }),
    auditable,
    async function ({ body, keyEntity, resolvedIp }: Request, res: Response, next: NextFunction) {
        try {
            res.send(await getMerchantService().loginPlayer(keyEntity, body, resolvedIp));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/terminals/create",
    authenticate,
    decodePid(),
    validateCreateData,
    auditable,
    async function ({ body, keyEntity: { id } }: Request, res: Response, next: NextFunction) {
        try {
            const terminal = await TerminalService.create(id, body);
            res.status(201).send(await terminal.toInfo());
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/info/:terminalId",
    authenticate,
    decodePid(),
    async function ({ keyEntity: { id }, params }: Request, res: Response, next: NextFunction) {
        try {
            const terminal = await TerminalService.getTerminal(id, params.terminalId);
            res.send(await terminal.toInfo());
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/games",
    authenticate,
    decodePid(),
    validateFilter,
    async function (req: Request, res: Response, next: NextFunction) {
        try {
            const games = await getFilteredGames(req.keyEntity, req, req.baseUrl + "/terminals");
            res.send(games);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/gamecategories",
    authenticate,
    validateFilter,
    validate({
        type: { optional: true, isGameCategoryType: true }
    }),
    async function ({ keyEntity, query }: Request, res: Response, next: NextFunction) {
        try {
            const gameCategories = await new GameCategoryService(keyEntity).findAllWithGamesForLobby(
                getBooleanParamFromQuery(query, "includeGames", false),
                query.currency,
                query.gameGroup
            );

            res.send(gameCategories);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/gamecategories/:gameCategoryId/games",
    authenticate,
    decodePid(),
    validateFilter,
    async function ({ keyEntity, params, query }: Request, res: Response, next: NextFunction) {
        try {
            const category = await new GameCategoryService(keyEntity).findOneById(params.gameCategoryId);
            const games = await GameCategoryGamesService.findAllForCategory(
                keyEntity.id,
                category,
                query
            );
            res.send(games.map(game => game.toInfo()));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/terminals/players/external/page",
    authenticate,
    decodePid(),
    auditable,
    async function ({ body, keyEntity }: Request, res: Response, next: NextFunction) {
        try {
            res.send(await getMerchantService().getPage(keyEntity, body));
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/games/live/:provider/live-info",
    authenticate,
    async function ({ baseUrl, params, query, keyEntity }: Request, res: Response, next: NextFunction) {
        try {
            const urlManager = await buildDynamicLiveManagerUrl(keyEntity);
            const data = await LiveManagerService.getLiveInfoForProviderTables(
                urlManager,
                params.provider,
                query["tableId__in"],
                baseUrl + "/terminals"
            );
            res.send(data);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.get("/terminals/fun/games/:gameCode",
    authenticate,
    validateGameCode,
    async function ({ keyEntity, params, query, resolvedIp }: Request, res: Response, next: NextFunction) {
        try {
            const playerGameURL = await UrlManager.getAnonymousGameURL({
                keyEntity,
                gameCode: params.gameCode,
                ip: resolvedIp,
                language: query.language,
                lobby: query.lobby,
                cashier: query.cashier
            });
            res.send(playerGameURL);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/refresh-captcha/:username",
    authenticate,
    validate({
        username: { notEmpty: true, isWord: true },
    }),
    async function ({ keyEntity: { key }, params }: Request, res: Response, next: NextFunction) {
        try {
            res.send(await generateCaptcha(key, params.username));
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/live-manager/url",
    authenticate,
    async function ({ keyEntity }: Request, res: Response, next: NextFunction) {
        try {
            const liveManagerUrl = await buildDynamicLiveManagerUrl(keyEntity);
            if (!liveManagerUrl) {
                return next(new Errors.DynamicDomainNotDefined());
            }
            res.send(liveManagerUrl);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/lobbies",
    authenticate,
    async function (req: Request, res: Response, next: NextFunction) {
        try {
            const lobbies = await LobbyCache.findAll(req.keyEntity);
            const { social, useSocialCasinoOperator } = await getEntitySettings(req.keyEntity.path);
            const liveManagerUrl = await buildDynamicLiveManagerUrl(req.keyEntity);
            const fields = parseCommaSeparatedString(req.query.fields, SHORT_INFO_FIELDS);
            res.send(lobbies.map(item => pick({
                ...item,
                info: {
                    ...item.info ?? {},
                    ...(social !== undefined ? { social } : {}),
                    ...(useSocialCasinoOperator !== undefined ? { useSocialCasinoOperator } : {}),
                    liveManagerUrl
                }
            }, fields)));
        } catch (err) {
            next(err);
        }
    });

router.get(["/terminals/lobbies/:lobbyId", "/terminals/lobby-info/:lobbyId"],
    decodePid({ forceReturnIfNumber: true }),
    authenticate,
    async function (req: Request, res: Response, next: NextFunction) {
        try {
            const item = await LobbyCache.findOne(req.keyEntity, req.params.lobbyId);
            const lobby = structuredClone(item);
            const { social, useSocialCasinoOperator } = await getEntitySettings(req.keyEntity.path);
            const liveManagerUrl = await buildDynamicLiveManagerUrl(req.keyEntity);
            lobby.info = {
                ...lobby.info ?? {},
                ...(social !== undefined ? { social } : {}),
                ...(useSocialCasinoOperator !== undefined ? { useSocialCasinoOperator } : {}),
                liveManagerUrl,
            }
            res.send(pick(lobby, parseCommaSeparatedString(req.query.fields, SHORT_INFO_FIELDS)));
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/lobbies/:lobbyId/options",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    async function ({ keyEntity, lobbyId }: Request & LobbyIdHolder, res: Response, next: NextFunction) {
        try {
            const options = await findLobbyOptions(keyEntity, lobbyId);
            res.send(options);
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/lobbies/:lobbyId/theme",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    async function ({ keyEntity, lobbyId }: Request & LobbyIdHolder, res: Response, next: NextFunction) {
        try {
            const theme = await findLobbyTheme(keyEntity, lobbyId);
            res.type("text/css").send(theme);
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/lobbies/:lobbyId/image/:name",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    async function ({ keyEntity, lobbyId, params: { name } }: Request & LobbyIdHolder, res: Response, next: NextFunction) {
        try {
            const image = await findLobbyImage(keyEntity, lobbyId, name);
            res.type(name === "icon" ? "image/x-icon" : "image/png").send(image);
        } catch (err) {
            next(err);
        }
    });

router.post("/terminals/lobbies/:lobbyId/players/external/login",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    auditable,
    async function ({ body, keyEntity, lobbyId, resolvedIp }: Request & LobbyIdHolder, res: Response, next: NextFunction) {
        try {
            const { token } = await getMerchantService().loginPlayer(keyEntity, body, resolvedIp);
            res.send({ token, lobbyId: encodeId(lobbyId) });
        } catch (err) {
            next(err);
        }
    });

router.post("/terminals/lobbies/:lobbyId/players/login",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    auditable,
    validate({
        code: { notEmpty: true, isWord: true },
        password: { notEmpty: true },
        force: { optional: true, isBoolean: true }
    }),
    async function ({ body, keyEntity, lobbyId, resolvedIp }: Request & LobbyIdHolder, res: Response, next: NextFunction) {
        try {
            const service = getTerminalPlayerLoginService(keyEntity, config.playerLoginToken);
            const info = await service.login(body, resolvedIp);
            res.send({ ...info, lobbyId: encodeId(lobbyId) });
        } catch (err) {
            next(err);
        }
    });

router.get("/terminals/lobbies/:lobbyId/refresh-captcha/:username",
    decodePid({ notRaiseError: true, ignoredKeys: ["lobbyId"] }),
    parseLobbyId,
    validate({
        username: { notEmpty: true, isWord: true },
    }),
    async function ({ keyEntity, params }: Request, res: Response, next: NextFunction) {
        try {
            res.send(await generateCaptcha(keyEntity.key, params.username));
        } catch (err) {
            next(err);
        }
    });

export default router;

import { Cache } from "./cache";
import { Models } from "../models/models";
import config from "../config";
import { DynamicDomainModel } from "../models/domain";

const model = Models.DynamicDomainModel;

const cache: Cache<string, DynamicDomainModel> = new Cache("dynamic-domain", searchInDb, {
    stdTTL: config.dynamicDomainCache.ttl,
    checkperiod: config.dynamicDomainCache.checkPeriod
});

async function searchInDb(id: string): Promise<DynamicDomainModel> {
    return model.findByPk(id);
}

export function reset(id?: number) {
    cache.reset(id && `${id}`);
}

export async function findOne(id: number): Promise<DynamicDomainModel> {
    return cache.find(`${id}`);
}

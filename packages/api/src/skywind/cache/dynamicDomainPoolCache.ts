import { Cache } from "./cache";
import { Models } from "../models/models";
import config from "../config";
import { DynamicDomainPoolDBInstance } from "../models/dynamicDomainPool";

const DynamicDomainModel = Models.DynamicDomainModel;
const DynamicDomainPoolModel = Models.DynamicDomainPoolModel;

const cache: Cache<string, DynamicDomainPoolDBInstance> = new Cache("static-domain-pool", searchInDb, {
    stdTTL: config.staticDomainPoolCache.ttl,
    checkperiod: config.staticDomainPoolCache.checkPeriod
});

async function searchInDb(id: string): Promise<DynamicDomainPoolDBInstance> {
    return DynamicDomainPoolModel.findByPk(id, {
        include: {
            model: DynamicDomainModel,
            as: "domains",
            through: {
                attributes: ["isActive"]
            }
        }
    });
}

export function reset(id?: number) {
    cache.reset(id && `${id}`);
}

export async function findOne(id: number): Promise<DynamicDomainPoolDBInstance> {
    return cache.find(`${id}`);
}

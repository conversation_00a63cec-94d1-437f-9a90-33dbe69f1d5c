import { Cache } from "./cache";
import config from "../config";
import { BaseEntity } from "../entities/entity";
import { EntityGame } from "../entities/game";
import * as GameService from "../services/game";

const cache: Cache<string, EntityGame> = new Cache("entity-game", searchInDb, {
    stdTTL: config.entityGameCache.ttl,
    checkperiod: config.entityGameCache.checkPeriod
});

async function searchInDb(id, entity: BaseEntity, gameCode: string, ignoreEntityGameNotFound?: boolean): Promise<EntityGame> {
    return GameService.findOneEntityGame(entity, gameCode, undefined, false, ignoreEntityGameNotFound);
}

export function reset(entity?: BaseEntity, gameCode?: string) {
    const key = entity && gameCode && `${entity.id}-${gameCode}`;
    cache.reset(key);
}

export async function findOne(entity: BaseEntity, gameCode: string, ignoreEntityGameNotFound?: boolean): Promise<EntityGame> {
    const key = `${entity.id}-${gameCode}`;
    return cache.find(key, entity, gameCode, ignoreEntityGameNotFound);
}

import { Cache } from "./cache";
import { Jurisdiction } from "../entities/jurisdiction";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import config from "../config";
import { BaseEntity } from "../entities/entity";

const cache: Cache<string, Jurisdiction> = new Cache("entity-jurisdiction", searchInDb, {
    stdTTL: config.entityJurisdictionCache.ttl,
    checkperiod: config.entityJurisdictionCache.checkPeriod
});

async function searchInDb(id) {
    const [jurisdiction] = await getEntityJurisdictionService().findAll({
        entityId: id
    });
    return jurisdiction;
}

export function reset(id?: string) {
    cache.reset(id);
}

export async function findOne(entity: BaseEntity): Promise<Jurisdiction> {
    const key = `${entity.id}`;
    return cache.find(key);
}

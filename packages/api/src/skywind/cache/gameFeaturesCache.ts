import { Cache } from "./cache";
import { GameFeatures } from "../entities/game";
import { getGame } from "../services/gameprovider";

export const gameFeaturesCache = new Cache<string, GameFeatures>(
    "game-codes-cache",
    async function(gameCode: string) {
        // This will throw error if the game is not found or is unavailable
        const game = await getGame(gameCode);
        return game.features;
    }
);

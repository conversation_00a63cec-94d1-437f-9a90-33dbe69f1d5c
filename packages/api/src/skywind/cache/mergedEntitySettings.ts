import { EntitySettings } from "../entities/settings";
import config from "../config";
import { Cache } from "./cache";
import { getMergedEntitySettings } from "../services/settings";

class MergedEntitySettingsCache extends Cache<string, EntitySettings>{
    constructor() {
        super(
            "merged-entity-settings-cache",
            function (keys: string[]) {
                return getMergedEntitySettings(keys);
            },
            {
                stdTTL: config.mergedEntitySettingsCache.ttl,
                checkperiod: config.mergedEntitySettingsCache.checkPeriod
            }
        );
    }

    protected retrieve(): Promise<EntitySettings> {
        return Promise.resolve(undefined);
    }

    public async find(path: string, keys: string[]): Promise<EntitySettings> {
        if (this.cache && this.connected) {
            let value: EntitySettings = this.cache.get(path);

            if (!value) {
                value = await this.search.call(this.search, keys);
                if (value.cacheMergedEntitySettings) {
                    if (value) {
                        this.cache.set(path, value);
                    } else {
                        this.cache.del(path);
                    }
                }
            }

            return value;
        } else {
            return this.search.call(this.search, keys);
        }
    }
}

export default new MergedEntitySettingsCache();

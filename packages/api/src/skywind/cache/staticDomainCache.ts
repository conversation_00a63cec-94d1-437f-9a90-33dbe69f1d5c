import { Cache } from "./cache";
import { Models } from "../models/models";
import config from "../config";
import { StaticDomainModel } from "../models/domain";

const model = Models.StaticDomainModel;

const cache: Cache<string, StaticDomainModel> = new Cache("static-domain", searchInDb, {
    stdTTL: config.staticDomainCache.ttl,
    checkperiod: config.staticDomainCache.checkPeriod
});

async function searchInDb(id: string): Promise<StaticDomainModel> {
    return model.findByPk(id);
}

export function reset(id?: number) {
    cache.reset(id && `${id}`);
}

export async function findOne(id: number): Promise<StaticDomainModel> {
    return cache.find(`${id}`);
}

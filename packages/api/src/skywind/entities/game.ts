import { Limits, LimitsByCurrencyCode } from "./gamegroup";
import { GameProvider } from "./gameprovider";
import { Label } from "./label";
import { JackpotTickerMapping } from "./jackpot";
import { DealerInfo, TableInfo, TableStatus, GameType } from "@skywind-group/sw-live-core";
import { Transaction } from "sequelize";
import { BaseEntity } from "./entity";
import { WALLET_TRX_TYPE } from "@skywind-group/sw-management-wallet";
import { BrandFinalizationType, GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { CurrencyFormatConfig } from "./settings";
import { ENTITY_GAME_STATUS } from "../utils/common";

export enum VirtualGameStatus {
    AVAILABLE = "available",
    UNAVAILABLE = "unavailable"
}

export interface Game {
    id?: number;
    code: string;
    title: string;
    type: string;
    url: string;
    providerId: number;
    providerGameCode: string;
    status: string;
    defaultInfo: GameDescription;
    info: GameDescriptionByLocale;
    limits: LimitsByCurrencyCode;
    gameProvider?: GameProvider;
    features?: GameFeatures;
    clientFeatures?: GameClientFeatures;
    historyRenderType: number;
    historyUrl?: string;
    labels?: Label[];
    createdAt?: string;
    limitsGroup?: string; // something like slot50, slot20 - where 50 and 20 is total bet
    countries?: string[]; // allowed countries for game
    totalBetMultiplier?: number;
    schemaDefinitionId?: number;
    deploymentGroupId?: number;
    defaultClientVersion?: string;
    physicalTableId?: string;
    updatedAt?: Date;
    comment?: string;
    created_at?: string;

    toCodeInfo(): GameCodeInfo;

    toProviderGameCodeInfo(): ProviderGameCodeInfo;

    toInfo(): GameInfo;

    toProviderInfo(): GameProviderInfo;

    // mode parameter can be provided by BO to get historyUrl instead of historyUrl2
    getHistoryUrl(entity: BaseEntity, mode?: string): Promise<string>;

    // GHM url
    getGameHistoryModuleUrl(entity: BaseEntity): Promise<string>;

    save(transaction?: Transaction): Promise<this>;

    isLiveGame(): boolean;

    getLimits(currency: string, copyLimitsFrom?: string): Promise<Limits>;
}

export interface GameRTP {
    baseRTP?: number;
    baseRTPRange?: {
        min: number;
        max: number;
    };
    jpRTP?: number;
    featuresRTP?: FeaturesRTP;

    supportsRtpConfigurator?: boolean;
}

export interface GameFeatures extends GameRTP {
    jackpotTypes?: string[];
    ignoreJackpotTypesValidation?: boolean;
    allowMissingContribution?: boolean;
    isFreebetSupported?: boolean;
    isExternalJackpotSupported?: boolean;
    isMultibet?: boolean;
    isBonusCoinsSupported?: boolean;
    isMarketplaceSupported?: boolean;
    transferEnabled?: boolean;
    isGRCGame?: boolean;
    supportsMarketingJP?: boolean;
    live?: Live;
    translations?: {
        [languageCode: string]: GameFeatureTranslationItem;
    };
    isFunModeNotSupported?: boolean;
    currenciesSupport?: string[];
    isCustomLimitsSupported?: boolean;
    decreaseMaxBetSupported?: boolean;
    increaseMinBetSupported?: boolean;
    gameFinalizationType?: GameFinalizationType;
    highestPrizeProbability?: number;
    gamble?: boolean;
    validateRequestsExtensionEnabled?: boolean;
    zeroBetCheckEnabled?: boolean;
    highestWin?: number;
    isSmartRoundingSupported?: boolean;
    gameLimitsCurrenciesVersion?: number;
    gameLimitsSettings?: Record<string, { copyLimitsFrom: string; }>;
    isForceFinishForbidden?: boolean;
    forbidOnlineRetries?: boolean;
    supportsReplay?: boolean;
    // ITG specific parameters
    licenseeId?: string;
    instantJpEnabled?: boolean;
    offlineWin?: boolean;
}

export type LiveGameType = "baccarat" | "roulette" | "blackjack" | "jokersWheel" | "andarBahar";

export type Live = LiveTable | LiveRush;

export interface ChatSettings {
    public?: boolean;
    private?: boolean;
}

interface LiveCommon {
    tableId: string;
    provider: string;
    chat?: ChatSettings;
    social?: boolean;
    useOperator?: string;
    providerSettings?: LiveProviderSettings;
}
export interface LiveTable extends LiveCommon {
    tableName?: string; // default table name in case of live provider doesn't return it
    selectionIntervalAfterNoMoreBets?: number;
    selectionIntervalBeforeNoMoreBets?: number;
    gameCode?: string;
    rushOrder?: number;
    isAddedToEntity?: boolean;
}

export interface LiveRush extends LiveCommon {
    type: LiveGameType;
    tables: LiveTable[];
    displayWinAmount?: boolean;
    leaveChipsOfWinsOnTable?: boolean;
    enableTip?: boolean;
}

export interface LiveProviderSettings {
    [field: string]: any;
}

export interface BrandClientFeatures {
    [field: string]: any;
}

export interface GameClientFeatures {
    [field: string]: any;
}

export interface GameFeatureTranslationItem {
    title: string;
    description?: string;
}

export interface GameDescription {
    name: string;
    description: string;

    // field - images, screen shots etc.
    [field: string]: any;
}

export interface GameDescriptionByLocale {
    [field: string]: GameDescription;
}

export interface GameCodeInfo {
    code: string;
}

export interface EntityGameCodeInfo extends GameCodeInfo, EntityGameData {
}

export interface ProviderGameCodeInfo {
    providerGameCode: string;
}

export interface EntityGameData {
    status?: string;
    settings?: GameSettings;
    limitFilters?: LimitFiltersByCurrency;
    urlParams?: EntityGameUrlParams;
    externalGameId?: string;
    domain?: string;
    title?: string;
    features?: EntityGameFeatures;
}

export interface GameCodesData {
    codes: string[];
}

export interface UpdateStatusesData extends GameCodesData {
    status: string;
    allowToFinishCurrentSession?: boolean;
}

export interface UpdateLimitsData extends GameCodesData {
    limitFilters: LimitsByCurrencyCode;
    merge?: boolean;
}

export interface EntityGame {
    id?: number;
    entityId: number;
    gameId: number;
    parentEntityGameId: number;
    parentGame?: EntityGame;
    status?: string;
    settings: GameSettings;
    externalGameId?: string;
    game?: Game;
    limitFilters?: LimitFiltersByCurrency;
    urlParams?: EntityGameUrlParams;
    domain?: string;
    title?: string;
    features?: EntityGameFeatures;

    isSuspended(): boolean;

    toInfo(isLobby?: boolean, addGameStatus?: boolean): EntityGameInfo;

    toCodeInfo(): EntityGameCodeInfo;

    settingsReady(): boolean;

    isLiveGame(): boolean;

    decreaseMaxBetSupported: boolean;
    increaseMinBetSupported: boolean;
    limitFiltersWillBeApplied: boolean;
    index?: number;
}

export interface GameSettings {
    jackpotId?: JackpotMapping;
    rtpConfigurator?: RtpConfigurator;
    marketing?: Marketing;
    newLimitsDisabled?: boolean;
    roundExpireAt?: number; // time when round context should be expired (in minutes)
    finalizationRetryPolicy?: {
        factor?: number;
        maxRetries?: number;
        initialRetryTimeout?: number;
    };
    startGameTokenExpiresIn?: number; // in seconds
    increaseMinBetSupported?: boolean;
    decreaseMaxBetSupported?: boolean;
    finalizationSupport?: BrandFinalizationType; // ability to override brand's behavior for concrete game
    limitFeaturesToMaxTotalStake?: boolean;
    countries?: string[]; // allowed countries for game
    mustWinJackpotBundled?: boolean;
    isForceFinishInsteadOfFinalizationSupported?: boolean;
    currencyFormatConfig?: CurrencyFormatConfig;
    smResultEnabled?: boolean;
    forwardToWrapper?: boolean;

    buyFeatureJrsdGrLegacy?: boolean;
    buyFeatureJrsdPtLegacy?: boolean;

    [field: string]: any;
}

export interface EntityGameUrlParams {
    modules?: string;
    balance_idle?: number;
    balance_ping?: number;
    keep_alive_idle?: number;
    keep_alive_ping?: number;

    disableBalancePing?: boolean;
    history_url?: string;
}

export interface EntityGameFeatures {
    translations?: {
        [languageCode: string]: {
            title: string;
        };
    };
}

export interface JackpotMapping {
    [jackpotType: string]: string;
}

export interface RtpConfigurator {
    rtp?: number;
    rtpDeduction?: number;
}

export interface Marketing {
    contributions: MarketingContribution[];
}

export interface MarketingContribution extends JackpotSettings {
    jackpotId: string;
    contribution: number;
}

export interface GameInfo {
    code: string;
    title: string;
    type: string;
    defaultInfo: GameDescription;
    info: GameDescriptionByLocale;
    limits: LimitsByCurrencyCode;
    labels: Label[];
    providerCode: string;
    providerTitle: string;
    features?: GameFeatures;
    clientFeatures?: GameClientFeatures;
    historyRenderType: number;
    historyUrl?: string;
    releaseDate?: string;
    limitsGroup?: string;
    countries?: string[];
    totalBetMultiplier?: number;
    schemaDefinitionId?: number;
    providerGameCode: string;
    url?: string;
    physicalTableId?: string;
}

export interface LiveGameInfo extends TableInfo {
    scoreboard: string;
}

export interface EntityGameInfo extends GameInfo {
    id?: number;
    gameId?: number;
    jackpots?: JackpotTickerMapping;
    live?: LiveGameInfo;
    rtpInfo?: {
        rtpTheoretical: number;
        rtpDeduction: number;
        rtpFinal: number;
    };
    limitFiltersWillBeApplied?: boolean;
    gameStatus?: string;
    status?: EntityGameData["status"];
    settings?: EntityGameData["settings"];
    limitFilters?: EntityGameData["limitFilters"];
    urlParams?: EntityGameData["urlParams"];
    domain?: EntityGameData["domain"];
    externalGameId?: EntityGameData["externalGameId"];
}

export interface EntityGameTerminalInfo {
    code: string;
    title?: string;
    type?: string;
    defaultInfo?: GameDescription;
    limits?: LimitsByCurrencyCode;
    providerTitle?: string;
    live?: LiveGameInfo;
    features?: GameFeatures;
}

export interface LobbyGamesLimitParams {
    includeGamesLimitRanges?: boolean;
    includeGamesLimits?: boolean;
    currency?: string;
    gameGroupName?: string;
    gameGroupId?: number;
}

export interface LobbyGamesParams extends LobbyGamesLimitParams {
    gameStatuses?: ENTITY_GAME_STATUS[];
    socketVersion?: string;
}

export interface GameLimitRange {
    name?: string;
    stakeMin: number;
    stakeMax: number;
    order?: number;
    isDefaultRoom?: boolean;
}

export interface LobbyGameInfo {
    code: string;
    type: string;
    gameModule?: string;
    title?: string;
    defaultInfo?: {
        images?: {
            poster?: string;
        };
        screenshots?: string[];
        screenshots_hd?: string[];
    };
    providerTitle?: string;
    features?: {
        isComission?: boolean;
        live?: GameFeatures["live"] & {
            gameType?: GameType;
            dealer?: DealerInfo;
            status?: TableStatus;
            language?: string;
            limits?: GameLimitRange[];
        };
        translations?: GameFeatures["translations"];
    };
    limits?: LimitsByCurrencyCode;
    isFavorite?: boolean;
    playedDate?: Date;
}

export interface GameProviderInfo {
    providerGameCode: string;
    title: string;
    type: string;
    url: string;
    defaultInfo: GameDescription;
    info: GameDescriptionByLocale;
    limits: LimitsByCurrencyCode;
    providerCode: string;
    providerTitle: string;
    features?: GameFeatures;
    clientFeatures?: GameClientFeatures;
    historyRenderType: number;
    historyUrl?: string;
    limitsGroup?: string;
    countries?: string[];
    totalBetMultiplier?: number;
    schemaDefinitionId?: number;
    defaultClientVersion?: string;
}

export interface PlayerGameURLInfo {
    url: string;
    token?: string;
    currency?: string;
    dynamicDomainHost?: string;
    staticDomainHost?: string;
}

export interface LobbyGameURLInfo extends PlayerGameURLInfo {
    game?: LobbyGameInfo;
    newPlayerToken?: string;
}

export interface LimitFiltersByCurrency {
    [field: string]: LimitFilters;
}

export interface LimitFilters {
    stakeMin?: number;
    stakeMax?: number;
    winMax?: number;
}

export interface JackpotSettings {
    id?: string;
    isGameOwned?: boolean;
    allowMissingContribution?: boolean;
    gameHistoryEnabled?: boolean;
    paymentStatisticEnabled?: boolean;
    winPaymentType?: string;

    [setting: string]: any;
}

export const DEFAULT_GAME_JACKPOT_SETTINGS: JackpotSettings = {
    isGameOwned: true,
    allowMissingContribution: false,
    gameHistoryEnabled: true,
    paymentStatisticEnabled: true,
    winPaymentType: WALLET_TRX_TYPE.JACKPOT
};

export const DEFAULT_MARKETING_JACKPOT_SETTINGS: JackpotSettings = {
    isGameOwned: false,
    allowMissingContribution: false,
    gameHistoryEnabled: false,
    paymentStatisticEnabled: false,
    winPaymentType: WALLET_TRX_TYPE.JACKPOT
};

export const DEFAULT_PHANTOM_JACKPOT_SETTINGS: JackpotSettings = {
    isGameOwned: false,
    allowMissingContribution: false,
    gameHistoryEnabled: true,
    paymentStatisticEnabled: true,
    winPaymentType: WALLET_TRX_TYPE.JACKPOT
};

export interface BulkAddEntityGamesResult {
    addedGamesCount: number;
}

export interface FavoriteGames {
    id?: number;
    entityId?: number;
    playerCode?: string;
    gameCode?: string;
    updatedAt?: Date;
    createdAt?: Date;
    isFavorite?: boolean;
    isRecently?: boolean;
}

export interface FeaturesRTP {
    [name: string]: { RTP: number, rtpReducer: boolean };
}

interface UpsertGamesRtp {
    gameCode: string;
    newRtpDeduction: number;
}

export interface BulkUpsertGamesRtp extends Array<UpsertGamesRtp> {
}

// tslint:disable-next-line:no-empty-interface
export interface FindOneGameOptions {
}

export const GAME_CODE_PATTERN = /^[\w-]+$/;
export const GAME_CODE_IN_PATTERN = /^[\w-, ]+$/;
export type ClientPlatform = "mobile" | "desktop";
export type FavoriteGame = Required<Omit<FavoriteGames, "id" | "createdAt">>;

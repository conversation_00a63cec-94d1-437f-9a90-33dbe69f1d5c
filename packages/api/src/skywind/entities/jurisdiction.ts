export interface EntityJurisdiction {
    entityId: number;
    jurisdictionId: number;
    jurisdiction?: Jurisdiction;
    createdAt?: Date;
}

export enum AllowedJackpotConfigurationLevel {
    NOT_ALLOWED = 0,
    SPECIFIC_BRAND_ONLY = 1,
    SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION = 2,
    SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION = 3,
    SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR = 4,
    SHARED_BETWEEN_SEVERAL_OPERATORS = 5,
    SHARED_GLOBALLY = 6,
    NO_RESTRICTIONS = 99
}

export interface Jurisdiction {
    id?: number;
    title?: string;
    code?: string;
    createdUserId?: number;
    updatedUserId?: number;
    createdAt?: Date;
    updatedAt?: Date;
    description?: string;
    allowedCountries?: string[];
    restrictedCountries?: string[];
    allowedJackpotConfigurationLevel?: AllowedJackpotConfigurationLevel;
    defaultCountry?: string;
    settings?: JurisdictionSettings;
    entityId?: number;
}
export interface ShortJurisdiction {
    code: string;
    title: string;
}
export interface GamePlayOptions {
    fastPlay?: boolean; // Triggered the next spin within less than 1 second of all reels stopping.
    turboPlus?: boolean; // There is an additional faster mode. Spin time of Turbo+ mode will be shorter than in Turbo.
    turbo?: boolean; // Is Turbo button enabled or disabled by default. (true - enabled)
}

export interface JurisdictionSettings extends GamePlayOptions {
    [field: string]: string | boolean | number | object;
    maxTotalStake?: number;
    showRTP?: boolean;
    rulesDateStamped?: boolean;
    autoPlayDisabled?: boolean;
    autoPlayRemovedFromInfo?: boolean;
    autoPlay100Limit?: boolean;
    lossLimitEnabled?: boolean;
    singleWinLimitEnabled?: boolean;
    stopAutoPlayOnJP?: boolean;
    showClockOnDesktop?: boolean;
    showClockOnMobile?: boolean;
    strictlySeparateButtons?: boolean;
    noAdaptiveProbability?: boolean;
    jpWinPushNotificationEnabled?: boolean;
    saveTurboModeState?: boolean;
    gamble?: boolean;
    dynamicMaxTotalBetLimitEnabled?: boolean;
    dynamicMaxTotalBetLimit?: {
        defaultMaxTotalBet?: number;
        defaultTotalBet?: number;
        currency?: string;
    };
}

export enum JurisdictionCodes {
    GREECE = "GR2",
    PORTUGAL = "PT"
}

export interface CreateData {
    title?: string;
    code: string;
    createdUserId: number;
    updatedUserId: number;
    description?: string;
    allowedCountries?: string[];
    restrictedCountries?: string[];
    defaultCountry?: string;
    settings?: JurisdictionSettings;
    allowedJackpotConfigurationLevel?: AllowedJackpotConfigurationLevel;
}

export interface UpdateData {
    title?: string;
    code?: string;
    updatedUserId: number;
    description?: string;
    allowedCountries?: string[];
    restrictedCountries?: string[];
    defaultCountry?: string;
    settings?: JurisdictionSettings;
    allowedJackpotConfigurationLevel?: AllowedJackpotConfigurationLevel;
}

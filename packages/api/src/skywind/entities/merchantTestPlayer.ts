import { RequiredByKeys } from "./typeUtils";

export enum SOURCE {
    INTEGRATION = "integration",
    SUPPORT = "support"
}

export interface MerchantTestPlayer {
    code: string;
    brandId: number;
    createdAt?: Date;
    updatedAt?: Date;
    startDate: Date;
    endDate?: Date;
    source: SOURCE;
}

export type CreateTestPlayer = RequiredByKeys<Partial<Omit<MerchantTestPlayer, "brandId" | "createdAt" | "updatedAt">>, "code">;

export interface UpdateData {
    endDate: MerchantTestPlayer["endDate"];
}

import { WithBalances } from "./entity";
import { Transaction } from "sequelize";
import { Audit } from "./audit";
import { FreebetGameConfig } from "./promotion";
import { PlayerWallet } from "@skywind-group/sw-management-wallet";
import { AgentAttributes } from "./agent";
import { GameGroupAttributes } from "./gamegroup";

export interface FindOptions {
    id?: number;
    code?: string;
}

export interface UpdateStatusesData {
    status: string;
    id: number[];
}

/**
 * For integrations with customers
 * Data which we expect within wallet transactions like deposit and withdraw
 */
export interface ExternalPayload {
    extRef: string;
    brandId: number;
}

export interface Player {
    id?: number;
    pid?: string;
    brandId: number;
    code: string;
    firstName: string;
    lastName: string;
    nickname?: string;
    email: string;
    salt: string;
    password: string;
    isPasswordTemp?: boolean;
    status: string;
    currency: string;
    country: string;
    language: string;
    gamegroupId: number;
    gamegroupName: string;
    agentId: number;
    agentTitle: string;
    agentDomain: string;
    lastLogin: Date;
    lastAction?: Date;
    deactivatedAt?: Date;
    isTest?: boolean;
    customData?: object;
    auditData?: Audit[];
    comments?: string;
    isVip?: boolean;

    readonly wallet: PlayerWallet;

    updateGameGroup(gameGroup: GameGroupAttributes);

    updateAgent(agent: AgentAttributes);

    toInfo(): Promise<PlayerInfo>;

    toShortInfo(): Promise<PlayerShortInfo>;

    toAuditInfo(): PlayerInfo;

    toInfoWithBalances(): Promise<PlayerInfoWithBalance>;

    isSuspended(): boolean;

    save(transaction?: Transaction): Promise<Player>;

}
export interface PlayerShortInfo {
    playerCode: string;
    isVip?: boolean;
    isPublicChatBlock?: boolean;
    isPrivateChatBlock?: boolean;
    isTracked?: boolean;
    hasWarn?: boolean;
    firstName?: string;
    lastName?: string;
    nickname?: string;
    nicknameChangeAttempts?: number;
    noBetNoChat?: boolean;
}
export interface PlayerInfo {
    id?: number;
    code: string;
    status: string;
    firstName?: string;
    lastName?: string;
    nickname?: string;
    email?: string;
    currency: string;
    country: string;
    language: string;
    gameGroup?: string;
    defaultGameGroup?: string;
    agentId?: number;
    agentTitle?: string;
    agentDomain?: string;
    isTest: boolean;
    lastLogin?: Date;
    lastAction?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    deactivatedAt?: Date;
    customData?: object;
    brandId?: string | number;
    brandTitle?: string;
    auditData?: PlayerAuditInfo[];
    comments?: string;
    isBlocked?: boolean;
    isVip?: boolean;
    isPublicChatBlock?: boolean;
    isPrivateChatBlock?: boolean;
    isTracked?: boolean;
    hasWarn?: boolean;
    noBetNoChat?: boolean;
    nicknameChangeAttempts?: number;
    nicknameChangeAttemptsLeft?: number; // used to send on init request
}

export interface PlayerAuditInfo {
    ts: Date;
    history: object;
    initiatorType: string;
    initiatorName: string;
    ip: string;
    userAgent: string;
}

export interface FreeBetsReward {
    promoId: number;
    amount: number;
    games: FreebetGameConfig[];
    currency: string;
    startDate: Date;
    endDate?: Date;
}

export interface BonusCoinsReward {
    promoId: number | string;
    amount: number;
    rewardedAmount: number;
    redeemMinAmount: number;
    redeemMaxAmount?: number;
    games: string[];
    startDate: Date;
    endDate: Date;
}

export interface Rewards {
    freeBets?: FreeBetsReward[];
    bonusCoins?: BonusCoinsReward[];
}

export interface WithRewards {
    rewards: Rewards;
}

export interface PlayerInfoWithBalance extends PlayerInfo, WithBalances, WithRewards {
    isOnline?: boolean;
}

export interface BlockedPlayerInfo {
    code: string;
    brandId: number;
    status: string;
    auditData?: PlayerAuditInfo[];
}

export const SELF_EXCLUSION_COUNT_DB_FIELD = "self_exclusion_count";

export enum PlayerStatus {
    NORMAL = "normal",
    SUSPENDED = "suspended",
}

export interface PlayerAttributes {
    id?: number;
    brandId?: number;
    code?: string;
    firstName?: string;
    lastName?: string;
    nickname?: string;
    email?: string;
    status?: string;
    salt?: string;
    password?: string;
    isPasswordTemp?: boolean;
    currency?: string;
    country?: string;
    language?: string;
    gamegroupId?: number;
    gamegroupName?: string;
    agentId?: number;
    agentTitle?: string;
    agentDomain?: string;
    customData?: object;
    lastLogin?: Date;
    deactivatedAt?: Date;
    isTest?: boolean;
    selfExclusionCount?: number;
    comments?: string;
    createdAt?: Date;
    updatedAt?: Date;
    isVip?: boolean;

    gamegroup?: any;
    agent?: any;
}

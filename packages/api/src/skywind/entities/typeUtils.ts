export type NonFunctionPropertyNames<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

export type NonFunctionProperties<T> = Pick<T, NonFunctionPropertyNames<T>>;

type AsyncFunction = (...args: any[]) => Promise<unknown>;
export type AsyncReturnType<Target extends AsyncFunction> = Awaited<ReturnType<Target>>;

/**
 * Type that makes the specified field mandatory, leaving other fields unchanged
 * @template T - Original type
 * @template K - Key of the field to be made mandatory
 */
export type RequiredByKeys<T, K extends keyof T = keyof T> = Omit<Required<Pick<T, K>> & Omit<T, K>, never>;

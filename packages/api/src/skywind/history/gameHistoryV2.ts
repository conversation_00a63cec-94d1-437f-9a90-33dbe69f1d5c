import * as FilterService from "../services/filter";
import { nativeFilters, sequelizeFindToStoredFunctionInput, StoredFunctionInput } from "../services/filter";
import { FindOptions, literal, where, col, Op } from "sequelize";
import { RoundHistory } from "../entities/gameHistory";
import {
    historyQueryField,
    mapRoundRowAndFixPrecision,
    queryParamsKeys,
    roundsHistoryMapping,
    sortableKeys
} from "./gameHistory";
import { getRoundHistoryModel } from "../models/roundHistory";
import * as Errors from "../errors";
import { postgresSlave } from "../storage/postgres";
import { QueryResult } from "pg";
import { BaseEntity } from "../entities/entity";
import config from "../config";
import { PagingHelper } from "../utils/paginghelper";
import { sequelizeSlave as db } from "../storage/db";
import { getChildIds } from "../services/entity";
import { EntitySettings } from "../entities/settings";
import EntitySettingsService from "../services/settings";
import EntityCache from "../cache/entity";
import { generateReplayToken, ReplayTokenData } from "../utils/token";
import { EntityGame } from "../entities/game";
import { getEntityStaticDomainService } from "../services/entityStaticDomainService";
import { getEntityDynamicDomainService } from "../services/entityDynamicDomainService";
import { getEntityGame } from "../services/entityGameService";
import { UrlPlaceholders } from "../services/gameUrl/urlPlaceholders";
import { getGameClientVersionService } from "../services/gameVersionService";
import { encodeId } from "../utils/publicid";
import { DomainStatus, StaticDomain, StaticDomainType } from "../entities/domain";
import { EntityStaticDomainPoolService } from "../services/entityStaticDomainPool";

const DEFAULT_SORT_KEY = "ts";
const DEFAULT_SORT_KEY_V2 = "insertedAt";
const DEFAULT_SORT_ORDER = "DESC";
const REPLAY_TOKEN_QUERY_PARAM = "rpt";
const GS_URL_QUERY_PARAM = "url";

const ROUND_HISTORY_QUERY: string = "SELECT *, (total_bet - total_win) AS revenue, " +
    "finished_at IS NOT NULL as finished FROM fnc_bo_rounds_history(p_where_filters => '{$whereFilters}', " +
    "p_sort_by => '{$sortBy}', p_limit => :limit, p_offset => :offset, " +
    "p_incl_sub_brands => {$inclSubBrands}, p_incl_test => {$includeTestBrands})";

const tsSortableFields: string[] = ["ts", "firstTs", "insertedAt"];

export interface GetGameContextsRequest {
    brandId: number;
    playerCode: string;
    gameCode: string;
}

export interface RoundHistoryService {
    findOne(brand: BaseEntity, query: any): Promise<RoundHistory>;

    getRounds(brandId: number | BaseEntity, query: any): Promise<RoundHistory[]>;

    updateQuery(query: any): any;

    getDefaultSortKey(): string;

    toInfo(dbItem): RoundHistory;
}

export class RoundHistoryServiceV1 implements RoundHistoryService {
    public async findOne(brand: BaseEntity, query: any = {}): Promise<RoundHistory> {
        query["brandId"] = brand.id;
        query.limit = 1;
        query.offset = 0;

        const [round]: RoundHistory[] = await this.getRounds(brand.id, query);
        return round;
    }

    public async getRounds(brandId: number | BaseEntity, query: any = {}): Promise<RoundHistory[]> {
        const sortBy = getSortByIgnoringValueFromQueryForTsFields(query, this.getDefaultSortKey());
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || DEFAULT_SORT_ORDER;

        this.updateQuery(query);

        const brand = typeof brandId !== "number" ? brandId : await EntityCache.findOne({ id: brandId });

        const offset = FilterService.valueFromQuery(query, "offset") || 0;
        const limit = FilterService.valueFromQuery(query, "limit") || 1000;
        const findOptions: FindOptions<any> = {
            attributes: {
                include: [
                    [literal("total_bet - total_win"), "revenue"],
                    [literal("finished_at is NOT NULL"), "finished"]
                ],
                exclude: ["gameId"],
            },
            where: query,
            offset,
            limit,
            order: [[col(sortBy), sortOrder]]
        };
        const model = getRoundHistoryModel();
        const entitySettings = await getEntitySettingsForHistory(brandId);
        const dbRowMapper = row => {
            const r: RoundHistory = this.toInfo(row);
            r.gameId = undefined;
            r["sessionId"] = undefined;
            r["ctrl"] = undefined;
            r.operator_site_id = undefined;

            if (entitySettings.addDecodedRoundIdToHistory) {
                r.game_id = r.roundId.toString();
            }

            if (entitySettings.hideBalanceBeforeAndAfter) {
                r.balanceAfter = undefined;
                r.balanceBefore = undefined;
            }

            if (r.currency && entitySettings.currencyFormatSettings) {
                const currencyFormatConfig = entitySettings.currencyFormatSettings[r.currency];
                if (currencyFormatConfig) {
                    r.currencyFormatConfig = currencyFormatConfig;
                }
            }

            return r;
        };

        if (config.gameHistory.useStoredProcedures) {
            let includeSubBrands = false;
            if (typeof brandId !== "number") {
                if (!brandId.isBrand()) {
                    includeSubBrands = true;
                }
                query["brandId"] = brandId.id;
            } else {
                query["brandId"] = brandId;
            }

            const funcInput: StoredFunctionInput = sequelizeFindToStoredFunctionInput(
                findOptions, roundsHistoryMapping);

            const queryWithParams: string = ROUND_HISTORY_QUERY
                .replace("$whereFilters", () => funcInput.whereFilters)
                .replace("$sortBy", funcInput.sortOrder)
                .replace("{$inclSubBrands}", includeSubBrands.toString())
                .replace("{$includeTestBrands}", "true");

            const results = await PagingHelper.findAllNative(queryWithParams,
                db,
                model,
                funcInput.limit || 1000,
                funcInput.offset || 0,
                dbRowMapper);
            return Promise.all(results.map(async result => {
                if (entitySettings.replayEnabled && result.finished && !result.totalJpWin) {
                    const entityGame = await getEntityGame(
                        brand,
                        result.gameCode,
                        false,
                        true,
                        true
                    );
                    if (entityGame?.game?.features?.supportsReplay) {
                        return {
                            ...result,
                            replayUrl: await this.getReplayUrl(entityGame, brand, result, entitySettings)
                        };
                    }
                }
                return result;
            }));
        }

        if (typeof brandId !== "number") {
            if (brandId.isBrand()) {
                query["brandId"] = brandId.id;
            } else {
                query["brandId"] = { [Op.in]: getChildIds(brandId) };
            }
        } else {
            query["brandId"] = brandId;
        }
        const items = await model.findAll(findOptions);
        return Promise.all(items.map(async item => {
            const mappedItem = dbRowMapper(item);
            if (mappedItem.finished && entitySettings.replayEnabled) {
                const entityGame = await getEntityGame(
                    brand,
                    mappedItem.gameCode,
                    false,
                    true,
                    true
                );
                if (entityGame?.game?.features?.supportsReplay) {
                    return {
                        ...mappedItem,
                        replayUrl: await this.getReplayUrl(entityGame, brand, mappedItem, entitySettings)
                    };
                }
            }
            return mappedItem;
        }));
    }

    private async getReplayUrl(
        entityGame: EntityGame,
        brand: BaseEntity,
        round: RoundHistory,
        entitySettings: EntitySettings
    ): Promise<string> {
        const { roundId, brandId } = round;
        const payload: ReplayTokenData = {
            roundId: Number.isNaN(+roundId) ? roundId : encodeId(+roundId),
            brandId: Number.isNaN(+brandId) ? brandId : encodeId(+brandId),
            currency: round.currency,
            gameCode: round.gameCode,
        };
        const dynamicDomain = await getEntityDynamicDomainService().get(brand, round.playerCode);
        const staticDomain = await this.getStaticDomain(brand, entityGame);
        const replaceParams = {
            dynamicDomain: dynamicDomain?.domain,
            staticDomain: staticDomain?.domain
        };
        let urlWithReplacements = UrlPlaceholders.replace(entityGame.game.url, replaceParams);
        if (urlWithReplacements.includes(UrlPlaceholders.KEY_CLIENT_VERSION)) {
            const clientVersion = await getGameClientVersionService().getGameClientVersion(brand, entityGame);
            urlWithReplacements = urlWithReplacements.replace(UrlPlaceholders.KEY_CLIENT_VERSION, clientVersion);
        }
        const processedUrl = new URL(urlWithReplacements);
        const gsUrlParam = processedUrl.searchParams.get(GS_URL_QUERY_PARAM);
        processedUrl.search = ""; // remove all query params
        if (gsUrlParam) {
            processedUrl.searchParams.append(GS_URL_QUERY_PARAM, gsUrlParam);
        }
        processedUrl.searchParams.append(REPLAY_TOKEN_QUERY_PARAM, await generateReplayToken(payload));

        const splash = entityGame.urlParams?.["splash"] || entitySettings.urlParams?.splash;
        if (splash) {
            processedUrl.searchParams.append("splash", splash);
        }

        return UrlPlaceholders.replace(processedUrl.toString(), replaceParams);
    }

    private async getStaticDomain(brand: BaseEntity, entityGame: EntityGame): Promise<StaticDomain> {
        if (entityGame.domain) {
            return { domain: entityGame.domain, type: StaticDomainType.STATIC, status: DomainStatus.ACTIVE };
        }
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(brand);
        const pickedStaticDomain = await entityStaticDomainPoolService.pickStaticDomain();
        return pickedStaticDomain || await getEntityStaticDomainService().get(brand);
    }

    public updateQuery(query: any) {
        const finished = query.finished && query.finished[Op.eq];

        if (["true", "false"].includes(finished)) {
            query.finished = where(literal("finished_at IS NOT NULL"), finished);
        } else {
            delete query.finished;
        }

        if (query["revenue"]) {
            query["revenue"] = where(literal("(total_bet - total_win)"), query["revenue"]);
        }
    }

    public toInfo(item): RoundHistory {
        return mapRoundRowAndFixPrecision(item.toJSON());
    }

    public getDefaultSortKey(): string {
        return DEFAULT_SORT_KEY;
    }
}

export class RoundHistoryServiceBackOfficeV1 implements RoundHistoryService {
    public async findOne(brand: BaseEntity, query: any = {}): Promise<RoundHistory> {
        query.brandId = brand.id;
        query.limit = 1;
        query.offset = 0;

        const [round]: RoundHistory[] = await this.getRounds(brand.id, query);
        return round;
    }

    public async getRounds(brandId: number | BaseEntity, query: any = {}): Promise<RoundHistory[]> {
        if (typeof brandId !== "number") {
            query["brandId"] = { [Op.in]: getChildIds(brandId) };
        } else {
            query["brandId"] = brandId;
        }
        const sortBy = getSortByIgnoringValueFromQueryForTsFields(query, this.getDefaultSortKey());
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || DEFAULT_SORT_ORDER;
        const offset = FilterService.valueFromQuery(query, "offset") || 0;
        const limit = FilterService.valueFromQuery(query, "limit");
        this.updateQuery(query);
        const whereFilter = nativeFilters(query, "bo_aggr_rounds", queryParamsKeys, historyQueryField);

        const sql = `WITH original_call AS ( \ 
           SELECT "round_id" AS "roundId", "brand_id" AS "brandId", \ 
                  "player_code" AS "playerCode", "game_code" AS "gameCode", \ 
                  "currency_code" AS "currency", "win", "bet", "revenue", \ 
                  "first_ts" AS "firstTs", "last_ts" AS "ts", "is_test" AS "isTest",\ 
                  "is_finished" AS "finished", "start_balance" AS "balanceBefore", "end_balance" AS "balanceAfter", \ 
                  "device_code" AS "device" \ 
           FROM   "bo_aggr_rounds" AS "bo_aggr_rounds" \ 
           ${whereFilter ? " WHERE " + whereFilter + "\n" : "\n"}
           ORDER BY "${sortBy}" ${sortOrder} \ 
           ${limit ? "LIMIT " + limit : ""} OFFSET '${offset}'), \ 
        old_table AS ( \
           SELECT *, ("firstTs" - interval '1 minute') AS sys_from, \ 
                  ("firstTs" + interval '1 minute') AS sys_to FROM original_call \ 
        ) \
        SELECT o."roundId", o."brandId", o."playerCode", o."gameCode", o."currency", \ 
               o."win", o."bet", o."revenue", o."firstTs", o."ts", o."isTest", o."finished", \ 
               o."balanceBefore", o."balanceAfter", o."device", \ 
               Coalesce(n.inserted_at, o."ts") AS "insertedAt", Coalesce(n.total_events, 0) AS "totalEvents" \ 
        FROM  old_table o \ 
              LEFT JOIN \ 
             rounds_history AS n ON o."roundId" = n.id AND n.started_at > sys_from AND n.started_at < sys_to \
        ORDER BY O."${sortBy}" ${sortOrder};`;

        const rs: QueryResult = await postgresSlave.query(sql);
        const entitySettings = await getEntitySettingsForHistory(brandId);
        const mapper = row => {
            const round: RoundHistory = this.toInfo(row);

            if (entitySettings.hideBalanceBeforeAndAfter) {
                round.balanceBefore = undefined;
                round.balanceAfter = undefined;
            }

            if (entitySettings.addDecodedRoundIdToHistory) {
                round.game_id = round.roundId.toString();
            }

            if (round.currency && entitySettings.currencyFormatSettings) {
                const currencyFormatConfig = entitySettings.currencyFormatSettings[round.currency];
                if (currencyFormatConfig) {
                    round.currencyFormatConfig = currencyFormatConfig;
                }
            }

            return round;
        };
        return rs.rows.map(mapper);
    }

    public updateQuery(query: any) {
        const finished = query.finished && query.finished[Op.eq];

        if (["true", "false"].includes(finished)) {
            return;
        } else {
            delete query.finished;
        }
    }

    public toInfo(item): RoundHistory {
        return mapRoundRowAndFixPrecision(item);
    }

    public getDefaultSortKey(): string {
        return DEFAULT_SORT_KEY;
    }
}

export class RoundHistoryServiceV2 extends RoundHistoryServiceV1 {
    public toInfo(item): RoundHistory {
        const round = { ...super.toInfo(item), internalRoundId: "" + item.roundId };

        return this.sanitizeJpInfo(round);
    }

    private sanitizeJpInfo(round: RoundHistory): RoundHistory {
        if (round.totalJpContribution === null) {
            round.totalJpContribution = 0;
        }

        if (round.totalJpWin === null) {
            round.totalJpWin = 0;
        }

        return round;
    }

    public updateQuery(query: any): void {
        super.updateQuery(query);

        replaceRoundId(query);
    }

    public getDefaultSortKey(): string {
        return DEFAULT_SORT_KEY_V2;
    }
}

export class RoundHistoryServiceBackOfficeV2 extends RoundHistoryServiceBackOfficeV1 {
    public toInfo(item): RoundHistory {
        return { ...super.toInfo(item), internalRoundId: "" + item.roundId };
    }

    public updateQuery(query: any): void {
        super.updateQuery(query);

        replaceRoundId(query);
    }
}

function replaceRoundId(query: {
    roundId: { $eq: number } | { $in: number[] },
    internalRoundId: { $eq: number } | { $in: number[] }
}) {

    if (query.roundId && query.internalRoundId) {
        throw new Errors.ValidationError("Allowed only roundId or internalRoundId");
    }

    if (query.internalRoundId) {
        query.roundId = query.internalRoundId;
        delete query.internalRoundId;
    }
}

/*
SWS-8623
Don't allow to filter by one date field (for example by firstTs, which is "Start TS") and order by another date field
(for example by "Ts", which is "Finish TS").
I think its easy to implement: if date field has been chosen for the filtering, then it should be automatically chosen
for sorting too.
 */
export function getSortByIgnoringValueFromQueryForTsFields(query: any, defaultSortBy: string): string {
    const sortByFromQuery: string = FilterService.getSortKey(query, sortableKeys, defaultSortBy);
    if (!tsSortableFields.includes(sortByFromQuery) || query[sortByFromQuery] !== undefined) {
        return sortByFromQuery;
    }
    const tsSortableFieldThatPresentInFilter: string = tsSortableFields.find(f => query[f] !== undefined);
    if (tsSortableFieldThatPresentInFilter) {
        return tsSortableFieldThatPresentInFilter;
    }
    return sortByFromQuery;
}

export async function getEntitySettingsForHistory(brand: number | BaseEntity): Promise<EntitySettings> {
    if (typeof brand !== "number") {
        return new EntitySettingsService(brand).get();
    }

    const realBrand = await EntityCache.findById(brand);

    return new EntitySettingsService(realBrand).get();
}

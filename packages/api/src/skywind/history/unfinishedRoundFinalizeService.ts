import {
    FinalizeBrokenPaymentType,
    FinalizeGameContextRequest,
    FinalizeResponse,
    RoundStatistic,
    StartFinalizeResponse
} from "../entities/gameHistory";
import { generateInternalToken } from "../utils/token";
import { OperationForbidden, ValidationError } from "../errors";
import { BrandEntity } from "../entities/brand";
import { GameServerApiProvider, getGameServerApiProvider } from "../services/deploymentGroup";
import { getEntityGame } from "../services/entityGameService";
import { Game } from "../entities/game";
import { getGame } from "../services/gameprovider";
import EntitySettingsService from "../services/settings";
import logger from "../utils/logger";
import { BrandFinalizationType, GameFinalizationType, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { GAME_TYPES } from "../utils/common";
import { ExternalFinalizeService } from "@skywind-group/sw-game-provider-ext-game-history";
import { UnfinishedRoundManagementServiceFactory } from "./unfinishedRoundManagementService";
import { decodeId, encodeId } from "../utils/publicid";

const START_FINALIZE_ENDPOINT = "gamerecovery/start-finalize";
const FINISH_FINALIZE_ENDPOINT = "gamerecovery/complete-finalize";
const FINALIZE_ENDPOINT = "gamerecovery/finalize";

const log = logger("unfinished-round-finalization-service");

export interface FinalizeService {

    startFinalize(merchantSessionId: string, playerCode: string, gameCode: string, currency?: string): Promise<StartFinalizeResponse>;

    completeFinalize(merchantSessionId: string, playerCode: string, currency?: string): Promise<void>;

    finalize(data: FinalizeGameContextRequest): Promise<RoundStatistic>;

    forceFinishUsingFinalization(data: FinalizeGameContextRequest): Promise<RoundStatistic>;
}

class FinalizeServiceImpl implements FinalizeService {

    private readonly gsApiProvider: GameServerApiProvider;

    constructor(public brand: BrandEntity) {
        this.gsApiProvider = getGameServerApiProvider();
    }

    public async startFinalize(merchantSessionId: string,
                               playerCode: string,
                               gameCode: string,
                               currency?: string): Promise<StartFinalizeResponse> {

        const brandFinalizationType = await this.getAndCheckBrandFinalizationType();
        const gameFinalizationType: GameFinalizationType = await this.getAndCheckGameFinalizationType(gameCode);

        const token = await generateInternalToken({
            brandId: this.brand.id,
            merchantSessionId,
            operatorSupportsFinalization: this.getOperatorSupportsFinalizationValue(brandFinalizationType),
            gameFinalizationType,
            brandFinalizationType
        });

        return this.gsApiProvider.sendPostByGameCode<StartFinalizeResponse>(
            START_FINALIZE_ENDPOINT,
            { token },
            this.brand,
            gameCode,
            playerCode
        );

    }

    public async completeFinalize(merchantSessionId: string,
                                  playerCode: string,
                                  currency?: string): Promise<void> {

        await this.getAndCheckBrandFinalizationType();
        const token = await generateInternalToken({
            brandId: this.brand.id,
            merchantSessionId
        });

        return this.gsApiProvider.sendPostByEntity<void>(FINISH_FINALIZE_ENDPOINT, { token }, this.brand, playerCode);
    }

    public async finalize(data: FinalizeGameContextRequest): Promise<RoundStatistic> {
        const gameCode = data.gameCode || data.round.gameCode;
        let brandFinalizationType: BrandFinalizationType;
        if (data.closeInSWWalletOnly) {
            brandFinalizationType = BrandFinalizationType.MANUAL_PAYMENTS;
        } else {
            brandFinalizationType = await this.getAndCheckBrandFinalizationType(gameCode);
        }
        data.gameFinalizationType = await this.getAndCheckGameFinalizationType(gameCode);
        data.brandFinalizationType = brandFinalizationType;

        const shouldForceFinishGame = await this.isForceFinishInsteadOfFinalizationSupported(
            gameCode,
            data.gameFinalizationType
        );

        if (!data.isManualApiCall && shouldForceFinishGame) {
            log.info(data, "Force-finish instead of finalization");
            const service = await UnfinishedRoundManagementServiceFactory.getUnfinishedRoundManagementService(
                this.brand
            );
            const response = await service.forceFinish(data.roundId, data.gameContextId);
            return response as any;
        }

        if (!data.gameFinalizationType || data.gameFinalizationType === GameFinalizationType.NONE) {
            throw new OperationForbidden(`Game ${gameCode} doesn't support finalization. Please check game settings`);
        }

        data.operatorSupportsFinalization = this.getOperatorSupportsFinalizationValue(brandFinalizationType);
        data.finalizeBrokenPayment = this.getFinalizeBrokenPaymentValue(brandFinalizationType);
        data.lockContext = this.getLockContextValue(brandFinalizationType);
        const token = await generateInternalToken(data);

        return this.gsApiProvider.sendPostByGameCode<RoundStatistic>(
            FINALIZE_ENDPOINT,
            { token },
            this.brand,
            gameCode,
            data.playerCode
        );
    }

    public async forceFinishUsingFinalization(data: FinalizeGameContextRequest): Promise<RoundStatistic> {
        const gameCode = data.gameCode || data.round.gameCode;
        const brandFinalizationType = BrandFinalizationType.FORCE_FINISH;

        data.gameFinalizationType = await this.getAndCheckGameFinalizationType(gameCode);
        data.operatorSupportsFinalization = this.getOperatorSupportsFinalizationValue(brandFinalizationType);
        data.brandFinalizationType = brandFinalizationType;
        data.finalizeBrokenPayment = this.getFinalizeBrokenPaymentValue(brandFinalizationType);
        data.lockContext = this.getLockContextValue(brandFinalizationType);
        const token = await generateInternalToken(data);
        return this.gsApiProvider.sendPostForForceFinish<RoundStatistic>(FINALIZE_ENDPOINT,
            { token },
            this.brand,
            data.gameContextId,
            gameCode);
    }

    /**
     * Gets Game Finalization type from game settings. If SWError occurred when trying to get finalization type,
     * such like GameNotFound - OperationForbidden error to be thrown.
     * Also, if game does not support finalization OperationForbidden is thrown
     */
    private async getAndCheckGameFinalizationType(gameCode: string): Promise<GameFinalizationType> {
        let gameFinalizationType: GameFinalizationType;
        try {
            gameFinalizationType = await this.getGameFinalizationType(gameCode);
        } catch (err) {
            if (err instanceof SWError) {
                log.warn(err, "Misconfiguration of finalization");
                throw new OperationForbidden(`${err.code}: ${err.message}`);
            }
            throw err; // Non SWError
        }

        return gameFinalizationType;
    }

    private async isForceFinishInsteadOfFinalizationSupported(
        gameCode: string,
        gameFinalizationType: GameFinalizationType
    ): Promise<boolean> {
        const entityGame = await getEntityGame(this.brand, gameCode, false, true);
        const service = new EntitySettingsService(this.brand);
        const settings = await service.get();
        const isForceFinishSupported = entityGame.settings?.isForceFinishInsteadOfFinalizationSupported ||
            settings.isForceFinishInsteadOfFinalizationSupported;
        return isForceFinishSupported && !entityGame.game.features?.isForceFinishForbidden &&
            (!gameFinalizationType || gameFinalizationType === GameFinalizationType.NONE);
    }

    private async getGameFinalizationType(gameCode: string): Promise<GameFinalizationType> {
        const game = await getGame(gameCode);
        return game.features?.gameFinalizationType;
    }

    /**
     * Gets Brand finalization type. If game code is present, then firstly checks the finalization setting in entityGame
     * as finalization setting in entityGame has higher priority.
     */
    private async getBrandFinalizationType(gameCode?: string): Promise<BrandFinalizationType> {
        const entityGame = gameCode && await getEntityGame(this.brand, gameCode, false, true);
        const service = new EntitySettingsService(this.brand);
        const settings = await service.get();
        return entityGame?.settings?.finalizationSupport || settings.finalizationSupport;
    }

    /**
     * Gets Brand Finalization type from entityGame or entity settings. If SWError occurred when trying to get
     * finalization type, such like GameIsSuspended, GameNotFound - OperationForbidden to be thrown.
     * Also, if brand does not support finalization OperationForbidden is thrown
     */
    private async getAndCheckBrandFinalizationType(gameCode?: string): Promise<BrandFinalizationType> {
        let brandFinalizationType;
        try {
            brandFinalizationType = await this.getBrandFinalizationType(gameCode);
        } catch (err) {
            if (err instanceof SWError) {
                log.warn(err, "Misconfiguration of finalization");
                throw new OperationForbidden(`${err.code}: ${err.message}`);
            }
            throw err; // Non SWError
        }
        if (brandFinalizationType === BrandFinalizationType.NOT_SUPPORTED) {
            throw new OperationForbidden(`Brand ${this.brand.name} does not support finalization`);
        }
        if (!brandFinalizationType) {
            throw new OperationForbidden(`Finalization is not allowed for Brand ${this.brand.name} if 'finalizationSupport' setting is not set`);
        }
        return brandFinalizationType;
    }

    private getOperatorSupportsFinalizationValue(finalizationType: BrandFinalizationType): boolean {
        return finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS
            || finalizationType === BrandFinalizationType.ROUND_STATISTICS
            || finalizationType === BrandFinalizationType.MANUAL_PAYMENTS;
    }

    private getFinalizeBrokenPaymentValue(brandFinalizationType: BrandFinalizationType): FinalizeBrokenPaymentType {
        if (brandFinalizationType === BrandFinalizationType.ROUND_STATISTICS
            || brandFinalizationType === BrandFinalizationType.MANUAL_PAYMENTS) {
            return FinalizeBrokenPaymentType.MARK_FINALIZED;
        }
        return FinalizeBrokenPaymentType.RETRY;
    }

    private getLockContextValue(brandFinalizationType: BrandFinalizationType): boolean {
        if (brandFinalizationType === BrandFinalizationType.ROUND_STATISTICS
            || brandFinalizationType === BrandFinalizationType.MANUAL_PAYMENTS) {
            return true;
        }
    }
}

class ExternalGameProviderFinalizeService implements FinalizeService {

    constructor(protected brand: BrandEntity, protected game: Game) {
        // empty
    }

    public async startFinalize(merchantSessionId: string,
                               playerCode: string,
                               gameCode: string,
                               currency?: string): Promise<StartFinalizeResponse> {

        const brandFinalizationType = await this.getBrandFinalizationType();
        const gameFinalizationType = this.game.features?.gameFinalizationType;

        const finalizeRequest = {
            brandId: this.brand.id,
            playerCode,
            gameCode: this.game.providerGameCode,
            gameProviderCode: this.game.gameProvider.code,
            gameFinalizationType,
            brandFinalizationType
        } as any;

        if (currency) {
            finalizeRequest.currency = currency;
        }

        return ExternalFinalizeService.startFinalize(finalizeRequest);
    }

    public async completeFinalize(merchantSessionId: string,
                                  playerCode: string,
                                  currency?: string): Promise<void> {

        const brandFinalizationType = await this.getBrandFinalizationType();
        const gameFinalizationType = this.game.features?.gameFinalizationType;

        const finalizeRequest = {
            brandId: this.brand.id,
            playerCode,
            gameCode: this.game.providerGameCode,
            gameProviderCode: this.game.gameProvider.code,
            gameFinalizationType,
            brandFinalizationType
        } as any;

        if (currency) {
            finalizeRequest.currency = currency;
        }

        return ExternalFinalizeService.completeFinalize(finalizeRequest);
    }

    public async finalize(data: FinalizeGameContextRequest): Promise<RoundStatistic> {

        const brandFinalizationType = await this.getBrandFinalizationType();
        const gameFinalizationType = this.game.features?.gameFinalizationType;

        const finalizeRequest = {
            brandId: this.brand.id,
            playerCode: data.playerCode,
            gameCode: this.game.providerGameCode,
            gameProviderCode: this.game.gameProvider.code,
            gameFinalizationType,
            brandFinalizationType
        } as any;

        if (data.currency) {
            finalizeRequest.currency = data.currency;
        }

        if (brandFinalizationType === BrandFinalizationType.NOT_SUPPORTED) {
            throw new OperationForbidden(`Brand ${this.brand.name} does not support finalization`);
        }
        if (!brandFinalizationType && data.isManualApiCall) {
            throw new OperationForbidden(`Manual finalization is not allowed for Brand ${this.brand.name} if 'finalizationSupport' setting is not set`);
        }

        return ExternalFinalizeService.finalize(finalizeRequest);
    }

    public forceFinishUsingFinalization(data: FinalizeGameContextRequest): Promise<RoundStatistic> {
        return Promise.resolve(undefined);
    }

    /**
     * Gets Brand finalization type. If game code is present, then firstly checks the finalization setting in entityGame
     * as finalization setting in entityGame has higher priority.
     */
    private async getBrandFinalizationType(gameCode?: string): Promise<BrandFinalizationType> {
        const entityGame = gameCode && await getEntityGame(this.brand, gameCode, false, true);
        const service = new EntitySettingsService(this.brand);
        const settings = await service.get();
        return entityGame?.settings?.finalizationSupport || settings.finalizationSupport;
    }
}

export function createFinalizeService(brand: BrandEntity, game: Game): FinalizeService {
    if (game && game.type === GAME_TYPES.external) {
        return new ExternalGameProviderFinalizeService(brand, game);
    }
    return new FinalizeServiceImpl(brand);
}

export class GameContextID {
    private constructor(public readonly gameCode: string,
                        public readonly brandId: number,
                        public readonly playerCode: string,
                        public readonly deviceId: string,
                        // ${common-prefix}:${context-prefix}:${brandId}:${playerCode}:${gameCode}:${deviceId}
                        private readonly idValue: string
    ) {
    }

    public static createFromString(id: string): GameContextID {
        const ids: string[] = id.split(":");

        return new GameContextID(ids[ids.length - 2],
            parseInt(ids[ids.length - 4], 10),
            ids[ids.length - 3],
            ids[ids.length - 1],
            id);
    }

    public asString(): string {
        return this.idValue;
    }

    /**
     * Traverse gameContextId string in order to replace brandId value with the encoded one
     */
    public static encodeBrandIdInGameContextId(gameContextId: string): string {
        let encodedBrandId = false;
        return gameContextId.split(":").reduce((result, currentKey, currentIndex) => {
            const separator = currentIndex === 0 ? "" : ":";
            // we assume that first numeric value in an array is brandId
            if (!encodedBrandId && !isNaN(+currentKey)) {
                currentKey = encodeId(+currentKey);
                encodedBrandId = true;
            }
            return result + separator + currentKey;
        }, "");
    }

    /**
     * Traverse gameContextId string in order to replace encoded brandId value with the decoded one
     */
    public static decodeBrandIdInGameContextId(gameContextId: string): string {
        return gameContextId.split(":").reduce((result, currentKey, currentIndex) => {
            const separator = currentIndex === 0 ? "" : ":";
            // we assume that third value in an array is brandId
            if (currentIndex === 2 && isNaN(+currentKey)) {
                currentKey = decodeId(currentKey).toString();
            }
            return result + separator + currentKey;
        }, "");
    }
}

export async function finalizeRound(brand: BrandEntity,
                                    contextId: string,
                                    waitForCompletion = true,
                                    closeInSWWalletOnly = false): Promise<FinalizeResponse> {
    if (!contextId) {
        return Promise.reject(new ValidationError("gameContextId param is required"));
    }
    const decodedGameContextId = GameContextID.decodeBrandIdInGameContextId(contextId);
    const gameContext: GameContextID = GameContextID.createFromString(decodedGameContextId);
    if (brand.id !== gameContext.brandId) {
        return Promise.reject(new OperationForbidden("brand id is different from brand in game context"));
    }
    const service = createFinalizeService(brand, undefined);

    const finalizePromise = service.finalize({
        gameContextId: decodedGameContextId,
        gameCode: gameContext.gameCode,
        isManualApiCall: true,
        closeInSWWalletOnly,
        playerCode: gameContext.playerCode
    }).then(undefined, (error) => {
        log.warn(`Finalization for gameContextId: ${decodedGameContextId} failed`);
        throw error;
    });

    if (waitForCompletion) {
        const roundStatistics = await finalizePromise;
        return { result: "finalized", roundStatistics };
    }

    return { result: "finalization-requested" };
}

export async function externalGameFinalizeRound(
    brand: BrandEntity,
    gameCode: string,
    playerCode: string,
    currency: string,
    waitForCompletion: boolean = true
): Promise<FinalizeResponse> {

    if (!gameCode || !playerCode) {
        return Promise.reject(new ValidationError("gameCode, playerCode params are required"));
    }

    const game: Game = await getGame(gameCode);
    if (game.type !== GAME_TYPES.external) {
        return Promise.reject(new ValidationError("Game should be external"));
    }

    const service = createFinalizeService(brand, game);

    const finalizePromise = service.finalize({
        brandId: brand.id,
        gameCode: gameCode,
        playerCode: playerCode,
        currency: currency,
        isManualApiCall: true
    }).then(undefined, (error) => {
        log.warn(`Finalization for ${brand.id}.${gameCode}.${playerCode} failed`);
        throw error;
    });

    if (waitForCompletion) {
        const roundStatistics = await finalizePromise;
        return { result: "finalized", roundStatistics };
    }

    return { result: "finalization-requested" };
}

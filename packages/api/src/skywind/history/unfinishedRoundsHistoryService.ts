import { getEntitySettingsForHistory, GetGameContextsRequest, RoundHistoryService } from "./gameHistoryV2";
import { UnfinishedRoundHistory, UnfinishedRoundStatus } from "../entities/gameHistory";
import { mapRoundRowAndFixPrecision } from "./gameHistory";
import logger from "../utils/logger";
import * as Errors from "../errors";
import { IncomingMessage } from "node:http";
import { BaseEntity } from "../entities/entity";
import { generateInternalToken } from "../utils/token";
import * as request from "request";
import { buildDynamicGSUrl } from "../services/entityDynamicDomainService";
import { getBooleanParamFromQuery } from "../api/middleware/middleware";
import { GameContextID } from "./unfinishedRoundManagementService";
import { EntitySettings } from "../entities/settings";
import { encodeId } from "../utils/publicid";

const log = logger("unfinished-history-service");
const DEFAULT_SORT_KEY = "firstTs";
const DEFAULT_SORT_ORDER = "DESC";
const UNFINISHED_ROUNDS_ENDPOINT = "unfinished/rounds";
const UNFINISHED_GAME_CONTEXTS_ENDPOINT = "unfinished/game-contexts";

// request object for game server
export interface UnfinishedRoundsHistoryRequest {
    brandId: number;
    playerCode?: string;
    gameCode?: string;
    ts__lt?: number;
    ts__lte?: number;
    ts__gt?: number;
    ts__gte?: number;
    status?: UnfinishedRoundStatus;
    roundId?: string | number;
    sortOrder?: "ASC" | "DESC";
    includeBrokenSpin?: boolean;
    gameContextId?: string;
}

export interface UnfinishedRoundHistoryRequest {
    brandId: number;
    roundId: string | number;
    includeBrokenSpin?: boolean;
}

export interface UnfinishedRoundsQuery {
    firstTs__lt?: string;
    firstTs__lte?: string;
    firstTs__gt?: string;
    firstTs__gte?: string;
    gameCode?: string;
    playerCode?: string;
    status?: UnfinishedRoundStatus;
    roundId?: string | number;
    sortOrder?: "ASC" | "DESC";
    includeBrokenSpin?: string;
    gameContextId?: string;
    brandId?: string | number;
    limit?: number;
    offset?: number;
}

export interface UnfinishedRoundQuery {
    roundId: string | number;
    includeBrokenSpin?: string;
    withTrx?: string;
}

function mapUnfinishedRound(
    item: UnfinishedRoundHistory,
    entitySettings: EntitySettings,
    withTrx: boolean
): UnfinishedRoundHistory {
    const round = {
        ...mapRoundRowAndFixPrecision(item),
        status: item.status
    } as UnfinishedRoundHistory;
    if (round.pendingSpin && round.pendingSpin.walletTransactionId && !withTrx) {
        delete round.pendingSpin.walletTransactionId;
    }
    if (round.gameContextId) {
        round.gameContextId = GameContextID.encodeBrandIdInGameContextId(round.gameContextId);
    }
    round["sessionId"] = undefined;
    round["ctrl"] = undefined;
    round.operator_site_id = undefined;

    if (entitySettings.hideBalanceBeforeAndAfter) {
        round.balanceAfter = undefined;
        round.balanceBefore = undefined;
    }

    if (round.currency && entitySettings.currencyFormatSettings) {
        const currencyFormatConfig = entitySettings.currencyFormatSettings[round.currency];
        if (currencyFormatConfig) {
            round.currencyFormatConfig = currencyFormatConfig;
        }
    }

    return round;
}

export class UnfinishedRoundsHistoryService implements RoundHistoryService {
    public async findOne(entity: BaseEntity, query: UnfinishedRoundsQuery): Promise<UnfinishedRoundHistory> {
        const [round]: UnfinishedRoundHistory[] = await this.getRounds(entity, query);
        return round;
    }

    public async getRounds(
        entity: BaseEntity,
        query: UnfinishedRoundsQuery & { withTrx?: string }
    ): Promise<UnfinishedRoundHistory[]> {
        const url = await buildDynamicGSUrl(entity, UNFINISHED_ROUNDS_ENDPOINT, query.playerCode);
        const token = await generateInternalToken(this.updateQuery({ ...query, brandId: entity.id }));
        const rounds = await get<UnfinishedRoundHistory[]>(url, { token });

        const withTrx = getBooleanParamFromQuery(query, "withTrx");
        const entitySettings = await getEntitySettingsForHistory(entity);
        return rounds.map(item => mapUnfinishedRound(item, entitySettings, withTrx));
    }

    public updateQuery(query: UnfinishedRoundsQuery & { brandId: number }): any {

        if (!query.playerCode && !query.roundId && !query.gameContextId) {
            throw new Errors.ValidationError("one of parameters should be present: playerCode, roundId, gameContextId");
        }
        const req: UnfinishedRoundsHistoryRequest = {
            brandId: query.brandId,
            sortOrder: query.sortOrder || DEFAULT_SORT_ORDER,
        };

        if (query.playerCode) {
            req.playerCode = query.playerCode;
        }

        if (query.gameCode) {
            req.gameCode = query.gameCode;
        }

        if (query.status) {
            req.status = query.status;
        }

        if (query.roundId !== undefined) {
            req.roundId = isNaN(query.roundId as any) ? query.roundId : encodeId(+query.roundId);
        }

        if (query.includeBrokenSpin !== undefined) {
            req.includeBrokenSpin = getBooleanParamFromQuery(query, "includeBrokenSpin");
        }

        if (query.gameContextId) {
            req.gameContextId = GameContextID.decodeBrandIdInGameContextId(query.gameContextId);
        }

        const prefixes = ["lt", "lte", "gt", "gte"];
        const firstTsKey = "firstTs";
        const tsKey = "ts";

        for (const prefix of prefixes) {
            const value = query[`${firstTsKey}__${prefix}`];
            if (value) {
                req[`${tsKey}__${prefix}`] = Date.parse(value);
            }
        }

        return req;

    }

    public getDefaultSortKey(): string {
        return DEFAULT_SORT_KEY;
    }

    public toInfo(item: any): UnfinishedRoundHistory {
        return { ...mapRoundRowAndFixPrecision(item), status: item.status, gameContextId: item.gameContextId };
    }

    public async getGameContexts(entity: BaseEntity, req: GetGameContextsRequest): Promise<any[]> {
        const url = await buildDynamicGSUrl(entity, UNFINISHED_GAME_CONTEXTS_ENDPOINT, req.playerCode);
        const token = await generateInternalToken(req);
        return await get<any[]>(url, { token });
    }
}

async function get<T>(url: string, query: any): Promise<T> {
    log.info(`Querying gameserver for unfinished rounds with url ${url} and params: ${JSON.stringify(query)}`);
    return new Promise<T>((resolve, reject) => {
        request.get(url, {
            headers: { "Content-Type": "application/json", "Accept": "application/json" },
            qs: query,
            json: true
        }, processResponse(resolve, reject));
    });
}

function processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => Promise<any> {
    return function (error: Error, response: IncomingMessage, body: any): Promise<any> {
        if (error) {
            log.error(error, "Failed to query game server");
            return reject(new Errors.ErrorQueryingGameServer());
        } else if (response.statusCode !== 200) {
            log.error({ body, statusCode: response.statusCode }, "Failed to get unfinished rounds");

            return reject(new Errors.ErrorQueryingGameServer(body.err && body.message ? body : undefined));

        } else {
            return resolve(body);
        }
    };
}

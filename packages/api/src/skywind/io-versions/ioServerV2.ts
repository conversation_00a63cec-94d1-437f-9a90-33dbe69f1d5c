import * as http from "node:http";
import * as socketIO from "socket.io-v2";
import { measures } from "@skywind-group/sw-utils";
import socketPlayer from "../api/socketPlayer";
import logger from "../utils/logger";
import measureProvider = measures.measureProvider;
import Metrics = measures.Metrics;
import config from "../config";

const log = logger("socket-v2");

export class IoServerV2 {
    public static createApplication(server: http.Server) {
        const options = {};
        if (config.socket.v2.path !== "") {
            options["path"] = config.socket.v2.path;
        }
        const io = socketIO(server, options);
        io.sockets.on("connect", (client) => {
            measureProvider.setGauge(Metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.connected).length);
            client.on("disconnect", () => {
                measureProvider.setGauge(Metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.connected).length);
            });
            socketPlayer(client, log);
        });
    }
}

import * as http from "node:http";
import { Server } from "socket.io";
import socketPlayer, { socketRoom } from "../api/socketPlayer";
import config from "../config";
import logger from "../utils/logger";
import { measureProvider, metrics } from "../utils/measures";

interface FavoriteGamePayload {
    entityId: number
    playerCode: string
    gameCode: string
    isFavorite: boolean
}

interface RecentlyGamePayload {
    entityId: number
    playerCode: string
    gameCode: string
}

type ServerToClientEvents = {
    "game-favorite": (data: { gameCode: string, isFavorite: boolean }) => void;
    "game-played": (data: { gameCode: string, time: number }) => void;
};

type ClientToServerEvents = {
    "get-player-info": (data: { token?: string }) => void;
};

const log = logger("socket-v4");

export class IoServerV4 {
    private static io: Server<ClientToServerEvents, ServerToClientEvents>;

    public static createApplication(server: http.Server) {
        if (!IoServerV4.io) {
            IoServerV4.io = IoServerV4.init(server);
        }
    }

    private static init(server: http.Server) {
        const io = new Server<ClientToServerEvents, ServerToClientEvents>(server, {
            path: config.socket.v4.path,
            allowEIO3: true,
        });

        io.on("connect", (socket) => {
            measureProvider.setGauge(metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.sockets.size).length);
            socket.on("disconnect", () => {
                measureProvider.setGauge(metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.sockets.size).length);
            });
        });

        io.on("connection", async function (socket) {
            socket.onAnyOutgoing(async (event, ...args) => {
                log.info({
                    socketId: socket.id,
                    rooms: socket.rooms,
                    event,
                    data: args
                }, "Socket message sent");
            });
            socket.on("disconnect", async (reason: string) => {
                log.info({
                    socketId: socket.id,
                    rooms: socket.rooms,
                    reason
                }, "Socket disconnected");
            });

            socketPlayer(socket, log);
        });
        return io;
    }

    public static notifyFavoriteGame({ entityId, playerCode, gameCode, isFavorite }: FavoriteGamePayload) {
        IoServerV4.io?.to(socketRoom(entityId, playerCode)).emit("game-favorite", { gameCode, isFavorite });
    }

    public static notifyRecentlyGame({ entityId, playerCode, gameCode }: RecentlyGamePayload) {
        IoServerV4.io?.to(socketRoom(entityId, playerCode)).emit("game-played", {
            gameCode,
            time: Date.now()
        });
    }
}

import { CreationOptional, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model, } from "sequelize";
import { sequelize as db } from "../storage/db";
import { EntityModel } from "./entity";
import { GameRTP, RtpConfigurator } from "../entities/game";
import { GameRtpHistory } from "../entities/gameRtpHistory";

export class GameRtpHistoryModel extends Model<
    InferAttributes<GameRtpHistoryModel>,
    InferCreationAttributes<GameRtpHistoryModel>
> {
    declare id: CreationOptional<number>;
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare gameId: number;
    declare gameCode: string;
    declare rtpInfo: GameRTP;
    declare rtpConfigurator: RtpConfigurator;
    declare ts: Date;

    public toInfo(): GameRtpHistory {
        return {
            id: this.id,
            entityId: this.entityId,
            gameId: this.gameId,
            gameCode: this.gameCode,
            rtpInfo: this.rtpInfo,
            rtpConfigurator: this.rtpConfigurator,
            ts: this.ts
        }
    }
}

GameRtpHistoryModel.init(
    {
        id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        entityId: { field: "entity_id", type: DataTypes.INTEGER, allowNull: true },
        gameId: { field: "game_id", type: DataTypes.INTEGER, allowNull: false },
        rtpInfo: { field: "rtp_info", type: DataTypes.JSONB, allowNull: false },
        rtpConfigurator: { field: "rtp_deduction", type: DataTypes.JSONB },
        ts: {
            field: "ts",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
    },
    {
        sequelize: db,
        modelName: "game_rtp_history",
        underscored: true,
        timestamps: false,
        tableName: "game_rtp_history"
    }
)

GameRtpHistoryModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export function getGameRtpHistoryModel() {
    return GameRtpHistoryModel;
}

import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    NonAttribute,
} from "sequelize";
import { EntityModel } from "./entity";
import { sequelize as db } from "../storage/db";

export class GameGroupModel extends Model<
    InferAttributes<GameGroupModel, { omit: "filters"}>,
    InferCreationAttributes<GameGroupModel, { omit: "filters"}>
> {
    declare id: CreationOptional<number>;
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare name: string;
    declare description: string;
    declare isDefault: boolean;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    // GameGroupFilterModel
    declare filters?: NonAttribute<any>;
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        unique: "gameGroupForBrand",
        references: {
            model: EntityModel,
            key: "id",
        },
    },
    name: { type: DataTypes.STRING, unique: "gameGroupForBrand", allowNull: false, validate: { notEmpty: true } },
    description: DataTypes.STRING,
    isDefault: {
        type: DataTypes.BOOLEAN,
        field: "is_default",
        defaultValue: false
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    }
};
GameGroupModel.init(
    schema,
    {
        modelName: "gamegroup",
        sequelize: db,
        tableName: "game_groups",
        underscored: true,
    }
)

GameGroupModel.belongsTo(EntityModel, { as: "brand" });

export function get() {
    return GameGroupModel;
}

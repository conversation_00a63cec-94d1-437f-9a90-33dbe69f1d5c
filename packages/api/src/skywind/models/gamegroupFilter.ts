import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { GameGroupFilter } from "../entities/gamegroup";
import { GameGroupModel } from "../models/gamegroup";
import { BaseEntity } from "../entities/entity";

export interface GameGroupFilterDBInstance extends Model<
        InferAttributes<GameGroupFilterDBInstance>,
        InferCreationAttributes<GameGroupFilterDBInstance>
    >,
    GameGroupFilter {
    gamegroup: GameGroupModel;
    get maxBetWillDecreased(): boolean;
    get minBetWillIncreased(): boolean;
    toInfo(entity?: BaseEntity, defaultGroup?: string): any;
}
export type GameGroupFilterModel = ModelStatic<GameGroupFilterDBInstance>;
const model: GameGroupFilterModel = db.define<GameGroupFilterDBInstance, GameGroupFilter>(
    "gameGroupFilters",
    {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        winCapping: { field: "win_capping", type: DataTypes.DECIMAL },
        maxTotalBet: { field: "max_total_bet", type: DataTypes.DECIMAL },
        minTotalBet: { field: "min_total_bet", type: DataTypes.DECIMAL },
        defTotalBet: { field: "def_total_bet", type: DataTypes.DECIMAL },
        maxExposure: { field: "max_exposure", type: DataTypes.DECIMAL },
        games: { field: "games", type: DataTypes.JSONB },
        currencies: { field: "currencies", type: DataTypes.JSONB },
        groupId: {
            field: "group_id",
            type: DataTypes.INTEGER,
            references: {
                model: GameGroupModel,
                key: "id"
            },
            onDelete: "CASCADE"
        },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        ignoreInvalid: { field: "ignore_invalid", type: DataTypes.BOOLEAN, defaultValue: false },
        maxBetWillDecreased: {
            type: DataTypes.VIRTUAL,
            get(): boolean {
                return !!(this.maxExposure || this.maxTotalBet);
            },
        },
        minBetWillIncreased: {
            type: DataTypes.VIRTUAL,
            get(): boolean {
                return !!this.minTotalBet;
            },
        },
    },
    {
        underscored: true,
        tableName: "game_group_filters"
    }
);
model.belongsTo(GameGroupModel, { foreignKey: "groupId", targetKey: "id", onDelete: "CASCADE" });
GameGroupModel.hasMany(model, { as: "filters", "foreignKey": "groupId" });

// Add prototype properties
Object.defineProperties(model.prototype, {
    toInfo: {
        value(this: GameGroupFilterDBInstance, entity?: BaseEntity, defaultGroup?: string): GameGroupFilter {
            const info: GameGroupFilter = {
                id: this.id,
                currencies: this.currencies,
                games: this.games,
                updatedAt: this.updatedAt,
                createdAt: this.createdAt
            };

            if (this.groupId) {
                info.groupId = this.groupId;
            }

            if (this.defTotalBet) {
                info.defTotalBet = +this.defTotalBet;
            }

            if (this.minTotalBet) {
                info.minTotalBet = +this.minTotalBet;
            }

            if (this.maxTotalBet) {
                info.maxTotalBet = +this.maxTotalBet;
            }

            if (this.winCapping) {
                info.winCapping = +this.winCapping;
            }

            if (this.maxExposure) {
                info.maxExposure = +this.maxExposure;
            }

            if (this.ignoreInvalid) {
                info.ignoreInvalid = this.ignoreInvalid;
            }

            if (entity && this.gamegroup) {
                const name = this.gamegroup.get("name");
                info.group = {
                    name,
                    description: this.gamegroup.get("description"),
                    isOwner: this.gamegroup.get("brandId") === entity.id,
                    isDefault: name === defaultGroup,
                    id: this.gamegroup.get("id")
                };
            }

            if (this.maxBetWillDecreased) {
                info.maxBetWillDecreased = this.maxBetWillDecreased;
            }

            if (this.minBetWillIncreased) {
                info.minBetWillIncreased = this.minBetWillIncreased;
            }

            return info;
        }
    }
});


export function getGameGroupFilterModel() {
    return model;
}

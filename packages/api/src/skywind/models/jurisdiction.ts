import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    NonAttribute,
} from "sequelize";
import { UserModel } from "./user";
import { EntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import {
    AllowedJackpotConfigurationLevel,
    Jurisdiction,
    JurisdictionSettings,
} from "../entities/jurisdiction";

export class JurisdictionModel extends Model<
    InferAttributes<JurisdictionModel>,
    InferCreationAttributes<JurisdictionModel>
> {
    declare id: CreationOptional<number>;
    declare title: string;
    declare code: string;
    declare createdUserId: ForeignKey<UserModel["id"]>;
    declare updatedUserId: ForeignKey<UserModel["id"]>;
    declare description: string;
    declare allowedCountries: string[];
    declare restrictedCountries: string[];
    declare allowedJackpotConfigurationLevel: AllowedJackpotConfigurationLevel;
    declare defaultCountry: string;
    declare settings: JurisdictionSettings;

    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(entityId?: number): Jurisdiction {
        const info: Jurisdiction = {
            id: this.id,
            title: this.title,
            code: this.code,
            description: this.description,
            createdUserId: this.createdUserId,
            updatedUserId: this.updatedUserId,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            allowedJackpotConfigurationLevel: this.allowedJackpotConfigurationLevel,
            settings: this.settings,
            entityId
        };

        if (this.allowedCountries) {
            info.allowedCountries = this.allowedCountries;
        }

        if (this.restrictedCountries) {
            info.restrictedCountries = this.restrictedCountries;
        }

        if (this.defaultCountry) {
            info.defaultCountry = this.defaultCountry;
        }

        return info;
    }
}
const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    title: { type: DataTypes.STRING, comment: "Title of jurisdiction" },
    code: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true }, unique: true },
    createdUserId: {
        type: DataTypes.INTEGER,
        field: "creator_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    updatedUserId: {
        type: DataTypes.INTEGER,
        field: "updater_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    description: { type: DataTypes.TEXT, comment: "Jurisdiction description" },
    settings: { field: "settings", type: DataTypes.JSONB, allowNull: false, comment: "Jurisdiction settings" },
    createdAt: { field: "created_at", type: DataTypes.DATE },
    updatedAt: { field: "updated_at", type: DataTypes.DATE },
    allowedCountries: {
        field: "allowed_countries",
        type: DataTypes.JSONB,
        allowNull: true
    },
    restrictedCountries: {
        field: "restricted_countries",
        type: DataTypes.JSONB,
        allowNull: true
    },
    defaultCountry: {
        field: "default_country",
        type: DataTypes.STRING,
        allowNull: true
    },
    allowedJackpotConfigurationLevel: {
        field: "allowed_jackpot_configuration_level",
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: AllowedJackpotConfigurationLevel.NO_RESTRICTIONS
    }
};

JurisdictionModel.init(
    schema,
    {
        sequelize: db,
        modelName: "jurisdiction",
        tableName: "jurisdictions",
        indexes: [
            { fields: ["creator_id"] },
            { fields: ["updater_id"] }
        ],
        comment: "Stores jurisdiction settings"
    }
);

export function get() {
    return JurisdictionModel;
}

// Jurisdiction to entity mapping:
export class EntityJurisdictionModel extends Model<
    InferAttributes<EntityJurisdictionModel, { omit: "jurisdiction" }>,
    InferCreationAttributes<EntityJurisdictionModel, { omit: "jurisdiction" }>
> {
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare jurisdictionId: ForeignKey<JurisdictionModel["id"]>;
    declare createdAt: CreationOptional<Date>;

    declare jurisdiction: NonAttribute<JurisdictionModel>;
}

const JurisdictionEntitySchema = {
    jurisdictionId: {
        primaryKey: true,
        type: DataTypes.INTEGER,
        field: "jurisdiction_id",
        references: {
            model: JurisdictionModel,
            key: "id",
            as: "jurisdiction"
        } as any,
        comment: "Jurisdiction id this jurisdiction mapping belongs to"
    },
    entityId: {
        primaryKey: true,
        type: DataTypes.INTEGER,
        field: "entity_id",
        references: {
            model: EntityModel,
            key: "id"
        },
        comment: "Entity to which this jurisdiction"
    },
    createdAt: { field: "created_at", type: DataTypes.DATE },
};
EntityJurisdictionModel.init(
    JurisdictionEntitySchema,
    {
        sequelize: db,
        modelName: "JurisdictionToEntityMapping",
        underscored: true,
        updatedAt: false,
        tableName: "jurisdiction_entity",
        indexes: [
            { fields: ["jurisdiction_id", "entity_id"] }
        ],
        comment: "Maps jurisdiction to entity"
    }
);

EntityJurisdictionModel.belongsTo(JurisdictionModel, { foreignKey: "jurisdictionId" });
EntityJurisdictionModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export function getEntityJurisdictionModel() {
    return EntityJurisdictionModel;
}

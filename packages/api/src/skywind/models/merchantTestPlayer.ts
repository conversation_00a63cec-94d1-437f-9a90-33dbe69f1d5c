import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    ForeignKey,
    CreationOptional, NonAttribute,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { EntityModel } from "./entity";
import { MerchantTestPlayer, SOURCE } from "../entities/merchantTestPlayer";

export interface TestMerchantPlayerDBInstance extends Model<
        InferAttributes<TestMerchantPlayerDBInstance>,
        InferCreationAttributes<TestMerchantPlayerDBInstance>
    >,
    MerchantTestPlayer {
    toInfo(): MerchantTestPlayer;
}

export class TestMerchantPlayerModel extends Model<
    InferAttributes<TestMerchantPlayerModel>,
    InferCreationAttributes<TestMerchantPlayerModel>
> {
    declare code: string;
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;
    declare startDate: CreationOptional<Date>;
    declare endDate: CreationOptional<Date>;
    declare source: SOURCE;

    declare entity?: NonAttribute<EntityModel>;

    public toInfo(): MerchantTestPlayer {
        return {
            code: this.code,
            brandId: this.brandId,
            source: this.source,
            startDate: this.startDate,
            endDate: this.endDate,
            updatedAt: this.updatedAt,
            createdAt: this.createdAt
        };
    }
}

const schema = {
    brandId: {
        field: "brand_id",
        type: DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: EntityModel,
            key: "id",
        }
    },
    code: { field: "code", type: DataTypes.STRING, primaryKey: true },
    createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
    },
    startDate: {
        field: "start_date",
        type: DataTypes.DATE,
    },
    endDate: {
        field: "end_date",
        type: DataTypes.DATE,
    },
    source: { field: "source", type: DataTypes.ENUM(...Object.values(SOURCE)) }
};

TestMerchantPlayerModel.init(
    schema,
    {
        sequelize: db,
        timestamps: true,
        freezeTableName: true,
        tableName: "merchant_test_players",
        modelName: "merchant_test_players",
    }
);

export function getTestMerchantPlayer() {
    return TestMerchantPlayerModel;
}

import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
} from "sequelize";
import { sequelize } from "../storage/db";

export class PermissionModel extends Model<
    InferAttributes<PermissionModel>,
    InferCreationAttributes<PermissionModel>
> {
    declare code: string;
    declare description: string;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;
}

const schema = {
    code: { field: "code", type: DataTypes.STRING, primaryKey: true },
    description: { field: "description", type: DataTypes.STRING, allowNull: false },
    createdAt: { field: "created_at", type: DataTypes.DATE },
    updatedAt: { field: "updated_at", type: DataTypes.DATE },
};

PermissionModel.init(
    schema,
    {
        tableName: "permissions",
        sequelize,
        underscored: true
    }
);

export function get() {
    return PermissionModel;
}

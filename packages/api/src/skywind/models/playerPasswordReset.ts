import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    Association,
} from "sequelize";
import { PlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

export class ResetPasswordModel extends Model<
    InferAttributes<ResetPasswordModel>,
    InferCreationAttributes<ResetPasswordModel>
> {
    declare id: CreationOptional<number>;
    declare guid: string;
    declare playerId: ForeignKey<PlayerModel["id"]>;
    declare expiredAt: Date;

    declare static associations: {
        player: Association<ResetPasswordModel, PlayerModel>;
    }
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
    },
    guid: {
        type: DataTypes.UUID,
        field: "guid",
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        references: {
            model: PlayerModel,
            key: "id",
        },
    },
    expiredAt: {
        type: DataTypes.DATE,
        field: "expired_at",
        allowNull: false,
    },
};
ResetPasswordModel.init(
    schema,
    {
        modelName: "player_password_reset",
        sequelize: db,
        tableName: "player_password_resets",
        indexes: [
            { fields: ["player_id"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

ResetPasswordModel.belongsTo(PlayerModel, { foreignKey: "playerId" });

export function get() {
    return ResetPasswordModel;
}

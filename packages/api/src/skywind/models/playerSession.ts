import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
} from "sequelize";
import { PlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

export class SessionModel extends Model<
        InferAttributes<SessionModel>,
        InferCreationAttributes<SessionModel>
    > {
    declare id: CreationOptional<number>;
    declare sessionId: string;
    declare playerId: ForeignKey<PlayerModel["id"]>;
    declare startedAt: Date;
    declare finishedAt: Date;
    declare finished: boolean
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
    },
    sessionId: {
        type: DataTypes.UUID,
        field: "session_id",
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        references: {
            model: PlayerModel,
            key: "id",
        },
    },
    startedAt: {
        type: DataTypes.DATE,
        field: "started_at",
        allowNull: false,
    },
    finishedAt: {
        type: DataTypes.DATE,
        field: "finished_at",
        allowNull: true,
    },
    finished: {
        type: DataTypes.BOOLEAN,
        field: "finished",
        allowNull: false,
        defaultValue: false,
    },
};
SessionModel.init(
    schema,
    {
        sequelize: db,
        modelName: "player_session",
        tableName: "player_sessions",
        indexes: [
            { fields: ["player_id"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

SessionModel.addHook("beforeValidate", function (instance) {
    if (!(instance as any).startedAt) {
        (instance as any).startedAt = new Date();
    }
});

SessionModel.belongsTo(PlayerModel, { foreignKey: "playerId" });

export function get() {
    return SessionModel;
}

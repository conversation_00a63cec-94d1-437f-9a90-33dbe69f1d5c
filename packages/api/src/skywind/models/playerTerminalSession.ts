import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    NonAttribute,
    Association,
} from "sequelize";
import { PlayerModel } from "./player";
import { SessionModel as PlayerSessionModel } from "./playerSession";
import { TerminalModel } from "./terminal";
import { sequelize as db } from "../storage/db";

export class PlayerTerminalSessionModel extends Model<
    InferAttributes<PlayerTerminalSessionModel, { omit: "terminal" }>,
    InferCreationAttributes<PlayerTerminalSessionModel, { omit: "terminal" }>
> {
    declare id: CreationOptional<number>;
    declare playerId: ForeignKey<PlayerModel["id"]>;
    declare terminalId: ForeignKey<TerminalModel["id"]>;
    declare playerSessionId: ForeignKey<PlayerSessionModel["id"]>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    declare terminal?: NonAttribute<TerminalModel>;

    declare static associations: {
        terminal: Association<PlayerTerminalSessionModel, TerminalModel>;
    }
}

const playerTerminalSchema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        autoIncrement: true,
        primaryKey: true,
        comment: "Auto-incremented id of instance",
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: PlayerModel,
            key: "id",
        },
        comment: "Reference to player",
    },
    terminalId: {
        type: DataTypes.INTEGER,
        field: "terminal_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: TerminalModel,
            key: "id",
        },
        comment: "Reference to terminal",
    },
    playerSessionId: {
        type: DataTypes.INTEGER,
        field: "player_session_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: PlayerSessionModel,
            key: "id",
        },
        comment: "Reference to player session",
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        comment: "When instance is created. Automatically added to db by Sequelize.",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        comment: "When instance is updated. Automatically changed in db by Sequelize.",
    },
};
PlayerTerminalSessionModel.init(
    playerTerminalSchema,
    {
        modelName: "player_terminals",
        tableName: "player_terminals",
        sequelize: db,
        underscored: true,
        timestamps: true,
    }
)

PlayerTerminalSessionModel.belongsTo(TerminalModel, { as: "terminal" });
TerminalModel.hasMany(PlayerTerminalSessionModel);
PlayerTerminalSessionModel.belongsTo(PlayerModel);
PlayerModel.hasMany(PlayerTerminalSessionModel);

export function get() {
    return PlayerTerminalSessionModel;
}

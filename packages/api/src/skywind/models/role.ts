import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    NonAttribute, Association,
} from "sequelize";
import { EntityModel } from "./entity";
import { UserModel } from "./user";
import { sequelize as db } from "../storage/db";
import { PermissionsList } from "../entities/user";

export class RoleModel extends Model<
    InferAttributes<RoleModel, { omit: "entity" }>,
    InferCreationAttributes<RoleModel, { omit: "entity" }>
> {
    declare id: CreationOptional<number>;
    declare title: string;
    declare description?: string;
    declare permissions: PermissionsList;
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare isShared: boolean;

    declare entity?: NonAttribute<EntityModel>;

    declare static associations: {
        entity: Association<RoleModel, EntityModel>,
    }
}

const roleSchema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    title: {
        type: DataTypes.STRING,
        validate: { notEmpty: true },
        allowNull: false,
    },
    description: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    permissions: {
        type: DataTypes.JSONB,
        allowNull: false,
    },
    entityId: {
        type: DataTypes.INTEGER,
        references: {
            model: EntityModel,
            key: "id",
        },
        field: "entity_id",
        allowNull: false,
    },
    isShared: {
        type: DataTypes.BOOLEAN,
        field: "is_shared",
        allowNull: false,
    },
};

RoleModel.init(
    roleSchema,
    {
        sequelize: db,
        modelName: "roles",
        tableName: "roles",
        underscored: true,
        indexes: [
            { fields: ["id"] },
            { fields: ["entity_id"] },
        ],
    }
);

RoleModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export class UserRoleModel extends Model<
    InferAttributes<UserRoleModel>,
    InferCreationAttributes<UserRoleModel>
> {
    declare userId: ForeignKey<UserModel["id"]>;
    declare roleId: ForeignKey<RoleModel["id"]>;
    declare createdAt: CreationOptional<Date>;
}

UserRoleModel.init(
    {
        userId: {
            type: DataTypes.INTEGER,
            references: {
                model: UserModel,
                key: "id",
            },
            field: "user_id",
            allowNull: false,
        },
        roleId: {
            type: DataTypes.INTEGER,
            references: {
                model: RoleModel,
                key: "id",
            },
            field: "role_id",
            allowNull: false,
        },
        createdAt: {
            type: DataTypes.DATE,
            field: "created_at",
        },
    },
    {
        sequelize: db,
        tableName: "user_roles",
        modelName: "user_roles",
        underscored: true,
        updatedAt: false,
        indexes: [
            { fields: ["role_id"] },
        ],
    }
)

RoleModel.belongsToMany(UserModel, { through: UserRoleModel, onDelete: "NO ACTION", onUpdate: "NO ACTION" });
UserModel.belongsToMany(RoleModel, { through: UserRoleModel, onDelete: "NO ACTION", onUpdate: "NO ACTION" });

export function getUserRoleModel() {
    return UserRoleModel;
}

export function getRoleModel() {
    return RoleModel;
}

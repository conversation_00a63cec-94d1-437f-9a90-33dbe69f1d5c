import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    NonAttribute,
    ForeignKey,
    Association,
} from "sequelize";
import { EntityModel } from "./entity";
import { LobbyModel } from "./lobby";
import { PlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

export class TerminalModel extends Model<
    InferAttributes<TerminalModel, { omit: "player" | "brand" | "lobby" }>,
    InferCreationAttributes<TerminalModel, { omit: "player" | "brand" | "lobby" }>
> {
    declare id: CreationOptional<number>;
    declare title: string;
    declare status: string;
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare playerId: ForeignKey<PlayerModel["id"]>;
    declare lobbyId: ForeignKey<LobbyModel["id"]>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    declare player?: NonAttribute<PlayerModel>;
    declare brand?: NonAttribute<EntityModel>;
    declare lobby?: NonAttribute<LobbyModel>;

    declare static associations: {
        brand: Association<TerminalModel, EntityModel>;
        player: Association<TerminalModel, PlayerModel>;
        lobby: Association<TerminalModel, LobbyModel>;
    }
}

const terminalSchema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        autoIncrement: true,
        primaryKey: true,
        comment: "Terminal auto-incremented id",
    },
    title: {
        type: DataTypes.STRING,
        field: "title",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Terminal title",
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        defaultValue: "normal",
        allowNull: false,
        validate: { notEmpty: true },
    },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Reference to brand",
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: true,
        references: {
            model: PlayerModel,
            key: "id",
        },
        comment: "Reference to logged player or null",
    },
    lobbyId: {
        type: DataTypes.INTEGER,
        field: "lobby_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: LobbyModel,
            key: "id",
        },
        comment: "Reference to lobby",
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        comment: "When terminal is created. Automatically added to db by Sequelize.",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        comment: "When terminal is updated. Automatically changed in db by Sequelize.",
    },
};
TerminalModel.init(
    terminalSchema,
    {
        sequelize: db,
        modelName: "terminals",
        tableName: "terminals",
        underscored: true,
        timestamps: true,
        indexes: [
            {
                name: "terminal_brand_ukey",
                unique: true,
                fields: ["title", "brand_id"],
            },
        ],
    }
);

TerminalModel.belongsTo(EntityModel, { as: "brand" });
TerminalModel.belongsTo(LobbyModel, { as: "lobby" });
TerminalModel.belongsTo(PlayerModel, { as: "player" });

export function get() {
    return TerminalModel;
}

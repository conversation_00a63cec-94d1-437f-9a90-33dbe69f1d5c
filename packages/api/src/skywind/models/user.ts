import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    NonAttribute,
    Association,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { AuthInfo, UserCustomData, UserStatus, UserType } from "../entities/user";
import { PasswordHistoryRecord } from "../services/user/user";
import { EntityModel } from "./entity";

export class UserModel extends Model<
    InferAttributes<UserModel, { omit: "roles" | "entity" }>,
    InferCreationAttributes<UserModel, { omit: "roles" | "entity" }>
> {
    declare id: CreationOptional<number>;
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare username: string;
    declare salt: string;
    declare password: string;
    declare email: string;
    declare status: UserStatus;
    declare firstName: string;
    declare lastName: string;
    declare authInfo: AuthInfo;
    declare passwordHistory: PasswordHistoryRecord[];
    declare userType: UserType;
    declare customData: UserCustomData;
    declare phone: string;
    declare lastLogin: Date;
    declare passwordChangedAt: Date;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;
    declare deletedAt: CreationOptional<Date>;
    declare version: number;

    declare entity?: NonAttribute<EntityModel>;

    // RoleModel
    declare roles?: NonAttribute<any>;

    declare static associations: {
        entity: Association<UserModel, EntityModel>;
        // RoleModel
        roles: Association<UserModel, any>;
    };
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    entityId: {
        type: DataTypes.INTEGER,
        field: "entity_id",
        allowNull: false,
        references: {
            model: EntityModel,
            key: "id",
        },
    },
    username: {
        type: DataTypes.STRING,
        field: "name",
        allowNull: false,
        validate: { notEmpty: true, is: /^[\w-]+$/ },
    },
    firstName: {
        type: DataTypes.STRING,
        field: "first_name",
        allowNull: true,
    },
    lastName: {
        type: DataTypes.STRING,
        field: "last_name",
        allowNull: true,
    },
    email: { type: DataTypes.STRING, allowNull: true, validate: { isEmail: true } },
    phone: { type: DataTypes.STRING, allowNull: true },
    password: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    salt: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    status: {
        type: DataTypes.ENUM(...Object.values(UserStatus)),
        allowNull: false,
        validate: { notEmpty: true }
    },
    authInfo: { field: "auth_info", type: DataTypes.JSONB, allowNull: true },
    passwordHistory: { field: "password_history", allowNull: false, type: DataTypes.JSONB },
    version: DataTypes.INTEGER,
    userType: {
        field: "user_type",
        type: DataTypes.ENUM(...Object.values(UserType)),
        allowNull: true
    },
    lastLogin: {
        type: DataTypes.DATE,
        field: "last_login",
        allowNull: true,
    },
    passwordChangedAt: {
        type: DataTypes.DATE,
        field: "password_changed_at",
        allowNull: true,
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
    deletedAt: {
        type: DataTypes.DATE,
        field: "deleted_at",
        allowNull: true,
    },
    customData: { field: "custom_data", allowNull: false, type: DataTypes.JSONB, defaultValue: {} },
};
UserModel.init(
    schema,
    {
        sequelize: db,
        modelName: "user",
        tableName: "users",
        indexes: [
            {
                name: "idx_users_entity_id_name_key_and_deleted_at_is_null",
                unique: true,
                fields: ["entity_id", "name"],
                where: {
                    deleted_at: null
                }

            },
            {
                name: "idx_users_entity_id_email_key_and_deleted_at_is_null",
                unique: true,
                fields: ["entity_id", "email"],
                where: {
                    deleted_at: null
                }
            },
        ],
        paranoid: true
    }
)

UserModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export function get() {
    return UserModel;
}
